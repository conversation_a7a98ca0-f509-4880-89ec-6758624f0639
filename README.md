# AWS Bedrock Data Automation Testing Framework

**Simple testing framework for AWS Bedrock accuracy with medical fax processing.**

This framework automates testing of AWS Bedrock Data Automation projects to measure per-field accuracy for medical fax information extraction.

## Key Features

- **Simple Accuracy Testing**: Basic exact matching with similarity fallback
- **AWS Bedrock Integration**: Direct integration with Bedrock Data Automation
- **CSV Reporting**: Detailed field-level accuracy reports
- **Batch Processing**: Test multiple documents efficiently

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Set up ground truth data:**
   ```bash
   python ground_truth_helper.py
   # This creates ground_truth_template.csv
   # Edit the CSV with your expected field values
   ```

## Configuration

### Environment Variables (.env file)

- `BEDROCK_PROJECT_ARN`: Your Bedrock Data Automation project ARN
- `INPUT_S3_BUCKET`: S3 bucket containing your PDF files
- `OUTPUT_S3_BUCKET`: S3 bucket for processing results
- `AWS_REGION`: AWS region (default: us-east-1)

### Ground Truth Data

Create a CSV file with the following structure:

```csv
document_name,patient_name,date_of_birth,diagnosis,medication,doctor_name,visit_date
sample_fax_1.pdf,John Doe,1980-01-15,Hypertension,Lisinopril 10mg,Dr. Johnson,2024-01-15
sample_fax_2.pdf,Jane Smith,1975-06-22,Diabetes,Metformin 500mg,Dr. Williams,2024-01-16
```

## Usage

### Quick Start

```bash
python run_tests.py
```

This will:
1. Load your ground truth data
2. Find all PDF files in your input S3 bucket
3. Process them through Bedrock Data Automation
4. Compare results against ground truth
5. Generate an accuracy report

### Manual Testing

```python
from bedrock_tester import BedrockDataAutomationTester

tester = BedrockDataAutomationTester(
    project_arn="your-project-arn",
    input_s3_bucket="your-input-bucket",
    output_s3_bucket="your-output-bucket"
)

# Test a single document
result = tester.run_single_test(
    s3_input_uri="s3://your-bucket/document.pdf",
    ground_truth={
        "patient_name": "John Doe",
        "diagnosis": "Hypertension"
    }
)

print(f"Overall accuracy: {result.overall_accuracy:.2%}")
```

## Output

The framework generates:

1. **Console output** with real-time progress
2. **accuracy_report.csv** with detailed per-document and per-field results
3. **Field-level accuracy summary** showing average accuracy for each extracted field

### Report Columns

- `document_name`: Name of the processed PDF
- `overall_accuracy`: Average accuracy across all fields
- `processing_time`: Time taken to process the document
- `status`: Processing status (COMPLETED/FAILED)
- `field_*_accuracy`: Individual field accuracy scores

## Accuracy Calculation

- **Exact Match**: 100% accuracy for identical values
- **Similarity Match**: Graduated scoring based on string similarity (>80% threshold)
- **Missing Fields**: 0% accuracy if field is missing from either extracted or ground truth data

## AWS Permissions Required

Your AWS credentials need the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeDataAutomationAsync",
                "bedrock:GetDataAutomationStatus"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-input-bucket/*",
                "arn:aws:s3:::your-output-bucket/*"
            ]
        }
    ]
}
```

## Troubleshooting

1. **"No test cases found"**: Ensure PDF files exist in your S3 bucket and ground truth file has matching document names
2. **Processing timeout**: Increase `max_wait_time` parameter for large documents
3. **Permission errors**: Verify AWS credentials and IAM permissions
4. **Missing ground truth**: Check that document names in CSV exactly match S3 object names
