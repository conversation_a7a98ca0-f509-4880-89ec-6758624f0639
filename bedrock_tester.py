import boto3
import json
import time
import pandas as pd
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import os
from dotenv import load_dotenv
from difflib import SequenceMatcher

load_dotenv()

@dataclass
class TestResult:
    document_name: str
    invocation_arn: str
    status: str
    extracted_data: Dict[str, Any]
    ground_truth: Dict[str, Any]
    field_accuracies: Dict[str, float]
    field_similarities: Dict[str, float]
    overall_accuracy: float
    overall_similarity: float
    processing_time: float

class BedrockDataAutomationTester:
    def __init__(self,
                 project_arn: str,
                 profile_arn: str = None,
                 region_name: str = 'us-east-1',
                 input_s3_bucket: str = None,
                 output_s3_bucket: str = None):

        self.project_arn = project_arn
        self.profile_arn = profile_arn or project_arn.replace(':data-automation-project/', ':data-automation-profile/')
        self.region_name = region_name
        self.input_s3_bucket = input_s3_bucket
        self.output_s3_bucket = output_s3_bucket

        self.runtime_client = boto3.client('bedrock-data-automation-runtime', region_name=region_name)
        self.data_automation_client = boto3.client('bedrock-data-automation', region_name=region_name)
        self.s3_client = boto3.client('s3', region_name=region_name)

        self.test_results = []
    
    def process_document(self, 
                        s3_input_uri: str, 
                        s3_output_uri: str,
                        document_name: str = None) -> str:
        
        if not document_name:
            document_name = s3_input_uri.split('/')[-1]
        
        try:
            response = self.runtime_client.invoke_data_automation_async(
                inputConfiguration={
                    's3Uri': s3_input_uri
                },
                outputConfiguration={
                    's3Uri': s3_output_uri
                },
                dataAutomationConfiguration={
                    'dataAutomationProjectArn': self.project_arn,
                    'stage': 'LIVE'
                },
                dataAutomationProfileArn=self.profile_arn
            )
            
            invocation_arn = response['invocationArn']
            print(f"Started processing {document_name}: {invocation_arn}")
            return invocation_arn
            
        except Exception as e:
            print(f"Error processing {document_name}: {str(e)}")
            raise
    
    def get_processing_status(self, invocation_arn: str) -> Dict[str, Any]:
        try:
            response = self.runtime_client.get_data_automation_status(
                invocationArn=invocation_arn
            )
            return response
        except Exception as e:
            print(f"Error getting status for {invocation_arn}: {str(e)}")
            raise
    
    def wait_for_completion(self, invocation_arn: str, max_wait_time: int = 300) -> Dict[str, Any]:
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            status_response = self.get_processing_status(invocation_arn)
            status = status_response.get('status', 'UNKNOWN')

            if status in ['Success', 'COMPLETED']:
                return status_response
            elif status in ['Failed', 'FAILED', 'ClientError']:
                error_msg = status_response.get('errorMessage', 'Unknown error')
                raise Exception(f"Processing failed for {invocation_arn}: {error_msg}")

            print(f"Status: {status}, waiting...")
            time.sleep(10)

        raise TimeoutError(f"Processing timed out for {invocation_arn}")
    
    def download_results(self, s3_output_uri: str) -> Dict[str, Any]:
        bucket = s3_output_uri.replace('s3://', '').split('/')[0]
        key = '/'.join(s3_output_uri.replace('s3://', '').split('/')[1:])

        try:
            # First download the metadata to get the custom output path
            response = self.s3_client.get_object(Bucket=bucket, Key=key)
            content = response['Body'].read().decode('utf-8')
            metadata = json.loads(content)

            # Extract the custom output path
            if 'output_metadata' in metadata and len(metadata['output_metadata']) > 0:
                segment_metadata = metadata['output_metadata'][0].get('segment_metadata', [])
                if len(segment_metadata) > 0:
                    custom_output_path = segment_metadata[0].get('custom_output_path')
                    if custom_output_path:
                        # Download the actual custom output
                        custom_bucket = custom_output_path.replace('s3://', '').split('/')[0]
                        custom_key = '/'.join(custom_output_path.replace('s3://', '').split('/')[1:])

                        custom_response = self.s3_client.get_object(Bucket=custom_bucket, Key=custom_key)
                        custom_content = custom_response['Body'].read().decode('utf-8')
                        custom_data = json.loads(custom_content)

                        # Return the inference_result which contains the extracted fields
                        return custom_data.get('inference_result', {})

            # Fallback to metadata if custom output not found
            return metadata

        except Exception as e:
            print(f"Error downloading results from {s3_output_uri}: {str(e)}")
            raise
    
    def calculate_field_accuracy_and_similarity(self, extracted_value: Any, ground_truth_value: Any) -> tuple[float, float]:
        if extracted_value is None and ground_truth_value is None:
            return 1.0, 1.0
        if extracted_value is None or ground_truth_value is None:
            return 0.0, 0.0

        extracted_str = str(extracted_value).strip()
        ground_truth_str = str(ground_truth_value).strip()

        # Calculate similarity using SequenceMatcher
        similarity = SequenceMatcher(None, extracted_str.lower(), ground_truth_str.lower()).ratio()

        # Exact match gets 100% accuracy
        if extracted_str == ground_truth_str:
            accuracy = 1.0
        # High similarity (>80%) gets the similarity score as accuracy
        elif similarity > 0.8:
            accuracy = similarity
        # Low similarity gets 0% accuracy
        else:
            accuracy = 0.0

        return accuracy, similarity
    
    def compare_results(self, extracted_data: Dict[str, Any], ground_truth: Dict[str, Any]) -> tuple[Dict[str, float], Dict[str, float]]:
        field_accuracies = {}
        field_similarities = {}

        for field, expected_value in ground_truth.items():
            extracted_value = extracted_data.get(field)
            accuracy, similarity = self.calculate_field_accuracy_and_similarity(extracted_value, expected_value)
            field_accuracies[field] = accuracy
            field_similarities[field] = similarity

        return field_accuracies, field_similarities
    
    def run_single_test(self,
                       s3_input_uri: str,
                       ground_truth: Dict[str, Any],
                       document_name: str = None) -> TestResult:

        if not document_name:
            document_name = s3_input_uri.split('/')[-1]

        s3_output_uri = f"s3://{self.output_s3_bucket}/results/{document_name}_results.json"

        start_time = time.time()

        invocation_arn = self.process_document(s3_input_uri, s3_output_uri, document_name)

        status_response = self.wait_for_completion(invocation_arn)

        # Get the actual output URI from the status response
        actual_output_uri = status_response.get('outputConfiguration', {}).get('s3Uri', s3_output_uri)

        extracted_data = self.download_results(actual_output_uri)

        field_accuracies, field_similarities = self.compare_results(extracted_data, ground_truth)
        overall_accuracy = sum(field_accuracies.values()) / len(field_accuracies) if field_accuracies else 0.0
        overall_similarity = sum(field_similarities.values()) / len(field_similarities) if field_similarities else 0.0

        processing_time = time.time() - start_time

        result = TestResult(
            document_name=document_name,
            invocation_arn=invocation_arn,
            status=status_response.get('status'),
            extracted_data=extracted_data,
            ground_truth=ground_truth,
            field_accuracies=field_accuracies,
            field_similarities=field_similarities,
            overall_accuracy=overall_accuracy,
            overall_similarity=overall_similarity,
            processing_time=processing_time
        )

        self.test_results.append(result)
        return result
    
    def run_batch_tests(self, test_cases: List[Dict[str, Any]]) -> List[TestResult]:
        results = []
        
        for i, test_case in enumerate(test_cases):
            print(f"\nProcessing test case {i+1}/{len(test_cases)}")
            
            try:
                result = self.run_single_test(
                    s3_input_uri=test_case['s3_input_uri'],
                    ground_truth=test_case['ground_truth'],
                    document_name=test_case.get('document_name')
                )
                results.append(result)
                
                print(f"Completed {result.document_name}: {result.overall_accuracy:.2%} accuracy, {result.overall_similarity:.2%} similarity")
                
            except Exception as e:
                print(f"Failed to process test case {i+1}: {str(e)}")
                continue
        
        return results
    
    def generate_report(self, output_file: str = "accuracy_report.csv"):
        if not self.test_results:
            print("No test results to report")
            return
        
        report_data = []
        
        for result in self.test_results:
            row = {
                'document_name': result.document_name,
                'overall_accuracy': result.overall_accuracy,
                'overall_similarity': result.overall_similarity,
                'processing_time': result.processing_time,
                'status': result.status
            }

            for field, accuracy in result.field_accuracies.items():
                row[f'field_{field}_accuracy'] = accuracy

            for field, similarity in result.field_similarities.items():
                row[f'field_{field}_similarity'] = similarity

            report_data.append(row)
        
        df = pd.DataFrame(report_data)
        df.to_csv(output_file, index=False)
        
        print(f"\nAccuracy Report Summary:")
        print(f"Total documents processed: {len(self.test_results)}")
        print(f"Average overall accuracy: {df['overall_accuracy'].mean():.2%}")
        print(f"Average overall similarity: {df['overall_similarity'].mean():.2%}")
        print(f"Average processing time: {df['processing_time'].mean():.1f} seconds")
        print(f"Report saved to: {output_file}")
        
        return df

def load_ground_truth_from_dict(ground_truth_dict: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    return ground_truth_dict

if __name__ == "__main__":
    PROJECT_ARN = os.getenv('BEDROCK_PROJECT_ARN')
    INPUT_BUCKET = os.getenv('INPUT_S3_BUCKET')
    OUTPUT_BUCKET = os.getenv('OUTPUT_S3_BUCKET')

    if not all([PROJECT_ARN, INPUT_BUCKET, OUTPUT_BUCKET]):
        print("Please set BEDROCK_PROJECT_ARN, INPUT_S3_BUCKET, and OUTPUT_S3_BUCKET environment variables")
        exit(1)

    tester = BedrockDataAutomationTester(
        project_arn=PROJECT_ARN,
        input_s3_bucket=INPUT_BUCKET,
        output_s3_bucket=OUTPUT_BUCKET
    )

    from ground_truth_data import GROUND_TRUTH_DATA

    test_cases = []
    for document_name, ground_truth in GROUND_TRUTH_DATA.items():
        test_cases.append({
            's3_input_uri': f's3://{INPUT_BUCKET}/{document_name}',
            'ground_truth': ground_truth,
            'document_name': document_name
        })

    results = tester.run_batch_tests(test_cases)
    tester.generate_report()
