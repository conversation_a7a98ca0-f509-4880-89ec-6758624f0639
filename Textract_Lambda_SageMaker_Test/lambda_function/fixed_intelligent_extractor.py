import re
import sys
import os
from typing import Dict, List, Any

class FixedIntelligentExtractor:
    def __init__(self):
        self.medical_terms = {
            'anatomical': ['head', 'brain', 'neck', 'chest', 'lung', 'heart', 'abdomen', 'pelvis', 'spine', 'shoulder', 'knee', 'ankle', 'wrist', 'hand', 'foot', 'elbow', 'hip', 'back', 'lumbar', 'cervical', 'thoracic'],
            'modalities': ['ct', 'mri', 'xray', 'x-ray', 'ultrasound', 'mammography', 'us', 'mm'],
            'directions': ['right', 'left', 'bilateral', 'r', 'l', 'rt', 'lt', 'anterior', 'posterior']
        }
        
        self.state_codes = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
            'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
            'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
            'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
            'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
            'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
            'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
            'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
        }
    
    def extract_all_fields(self, text: str) -> Dict[str, str]:
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        text_lower = text.lower()
        
        results = {}
        
        results['patient_phone_number'] = self._find_patient_phone_number(lines, text)
        results['doctor_phone_number'] = self._find_doctor_phone_number(lines, text)
        results['doctor_fax'] = self._find_fax_number(lines, text)
        results['first_name'] = self._find_first_name(lines, text)
        results['last_name'] = self._find_last_name(lines, text)
        results['fullname'] = self._find_full_name(lines, text)
        results['birthDate'] = self._find_birth_date(lines, text)
        results['gender'] = self._find_gender(lines, text)
        results['doctor_X'] = self._find_doctor_name(lines, text)
        results['doctor_NPI'] = self._find_npi(lines, text)
        results['doctor_address'] = self._find_address(lines, text)
        results['doctor_city'] = self._find_city(lines, text)
        results['doctor_state'] = self._find_state(lines, text)
        results['icd_codes'] = self._find_icd_codes(lines, text)
        results['modality'] = self._find_modality(text_lower, text)
        results['exam'] = self._find_exam(lines, text_lower)
        results['bodyPart'] = self._find_body_part(text_lower, text)
        results['insurance_number'] = self._find_insurance_number(lines, text)
        results['doctor_exam_codes'] = self._find_exam_codes(lines, text)
        
        return self._post_process(results)
    
    def _find_patient_phone_number(self, lines: List[str], full_text: str) -> str:
        phone_patterns = [
            r'\((\d{3})\)\s*(\d{3})[-\s]*(\d{4})',
            r'(\d{3})[-\.\s](\d{3})[-\.\s](\d{4})',
            r'(\d{3})\s*(\d{3})\s*(\d{4})'
        ]
        
        patient_keywords = ['patient', 'pt', 'name', 'client']
        exclude_keywords = ['doctor', 'dr', 'physician', 'provider', 'referring', 'ordering', 'fax']
        
        for i, line in enumerate(lines):
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in patient_keywords):
                context_lines = lines[max(0, i-2):i+3]
                
                for context_line in context_lines:
                    if not any(exclude in context_line.lower() for exclude in exclude_keywords):
                        for pattern in phone_patterns:
                            match = re.search(pattern, context_line)
                            if match:
                                if len(match.groups()) == 3:
                                    return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
                                else:
                                    digits = re.sub(r'[^\d]', '', match.group(0))
                                    if len(digits) == 10:
                                        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        
        for line in lines:
            if not any(exclude in line.lower() for exclude in exclude_keywords):
                for pattern in phone_patterns:
                    match = re.search(pattern, line)
                    if match:
                        if len(match.groups()) == 3:
                            return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
                        else:
                            digits = re.sub(r'[^\d]', '', match.group(0))
                            if len(digits) == 10:
                                return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        
        return ""
    
    def _find_doctor_phone_number(self, lines: List[str], full_text: str) -> str:
        phone_patterns = [
            r'\((\d{3})\)\s*(\d{3})[-\s]*(\d{4})',
            r'(\d{3})[-\.\s](\d{3})[-\.\s](\d{4})',
            r'(\d{3})\s*(\d{3})\s*(\d{4})'
        ]
        
        doctor_keywords = ['doctor', 'dr', 'physician', 'provider', 'referring', 'ordering']
        exclude_keywords = ['patient', 'pt', 'fax']
        
        for i, line in enumerate(lines):
            line_lower = line.lower()
            
            if any(keyword in line_lower for keyword in doctor_keywords):
                context_lines = lines[max(0, i-2):i+3]
                
                for context_line in context_lines:
                    if not any(exclude in context_line.lower() for exclude in exclude_keywords):
                        for pattern in phone_patterns:
                            match = re.search(pattern, context_line)
                            if match:
                                if len(match.groups()) == 3:
                                    return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
                                else:
                                    digits = re.sub(r'[^\d]', '', match.group(0))
                                    if len(digits) == 10:
                                        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        
        return ""
    
    def _find_fax_number(self, lines: List[str], full_text: str) -> str:
        phone_patterns = [
            r'\((\d{3})\)\s*(\d{3})[-\s]*(\d{4})',
            r'(\d{3})[-\.\s](\d{3})[-\.\s](\d{4})',
            r'(\d{3})\s*(\d{3})\s*(\d{4})'
        ]
        
        for line in lines:
            if 'fax' in line.lower():
                for pattern in phone_patterns:
                    match = re.search(pattern, line)
                    if match:
                        if len(match.groups()) == 3:
                            return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
                        else:
                            digits = re.sub(r'[^\d]', '', match.group(0))
                            if len(digits) == 10:
                                return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        
        return ""
    
    def _find_first_name(self, lines: List[str], full_text: str) -> str:
        patient_indicators = ['patient', 'pt', 'name', 'first']
        exclude_words = ['signature', 'doctor', 'dr', 'physician', 'last', 'family', 'form', 'field']
        
        for line in lines:
            line_lower = line.lower()
            if any(indicator in line_lower for indicator in patient_indicators):
                words = re.findall(r'\b[A-Z][a-z]{2,}\b', line)
                for word in words:
                    if word.lower() not in exclude_words and len(word) > 2:
                        return word
        
        name_patterns = [
            r'Patient:\s*([A-Z][a-z]+)',
            r'Name:\s*([A-Z][a-z]+)',
            r'First:\s*([A-Z][a-z]+)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, full_text)
            if match:
                name = match.group(1)
                if name.lower() not in exclude_words:
                    return name
        
        return ""

    def _find_birth_date(self, lines: List[str], full_text: str) -> str:
        date_patterns = [
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})',
            r'(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})'
        ]

        birth_keywords = ['dob', 'birth', 'born', 'date of birth', 'birthday']

        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in birth_keywords):
                for pattern in date_patterns:
                    match = re.search(pattern, line)
                    if match:
                        parts = match.groups()

                        if len(parts[0]) == 4:
                            year, month, day = parts
                        elif len(parts[2]) == 4:
                            month, day, year = parts
                        else:
                            month, day, year = parts
                            year = f"20{year}" if int(year) < 50 else f"19{year}"

                        return f"{day.zfill(2)}/{month.zfill(2)}/{year}"

        for pattern in date_patterns:
            matches = re.findall(pattern, full_text)
            for match in matches:
                if len(match[0]) == 4:
                    year, month, day = match
                elif len(match[2]) == 4:
                    month, day, year = match
                else:
                    month, day, year = match
                    year = f"20{year}" if int(year) < 50 else f"19{year}"

                if 1900 <= int(year) <= 2024:
                    return f"{day.zfill(2)}/{month.zfill(2)}/{year}"

        return ""

    def _find_gender(self, lines: List[str], full_text: str) -> str:
        gender_patterns = [
            r'(?:gender|sex):\s*(male|female|m|f)\b',
            r'\b(male|female)\b',
            r'\b(m|f)\b(?=\s|$|,)'
        ]

        for line in lines:
            line_lower = line.lower()
            if any(word in line_lower for word in ['gender', 'sex']):
                for pattern in gender_patterns:
                    match = re.search(pattern, line_lower)
                    if match:
                        gender = match.group(1).lower()
                        if gender in ['m', 'male']:
                            return 'Male'
                        elif gender in ['f', 'female']:
                            return 'Female'

        return ""

    def _find_doctor_name(self, lines: List[str], full_text: str) -> str:
        doctor_keywords = ['doctor', 'dr', 'physician', 'referring', 'ordering', 'provider']
        exclude_words = ['patient', 'signature', 'form', 'field']

        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in doctor_keywords):
                line_clean = re.sub(r'\b(?:dr\.?|doctor|md|do|phd)\b', '', line, flags=re.IGNORECASE)
                words = re.findall(r'\b[A-Z][a-z]{2,}\b', line_clean)
                name_words = [w for w in words if w.lower() not in exclude_words and w.lower() not in doctor_keywords]
                if len(name_words) >= 2:
                    return ' '.join(name_words[:3])

        doctor_patterns = [
            r'(?:Dr\.?\s+|Doctor\s+)([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'Referring:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)',
            r'Ordering:\s*([A-Z][a-z]+\s+[A-Z][a-z]+)'
        ]

        for pattern in doctor_patterns:
            match = re.search(pattern, full_text)
            if match:
                name = match.group(1)
                if not any(exclude in name.lower() for exclude in exclude_words):
                    return name

        return ""

    def _find_npi(self, lines: List[str], full_text: str) -> str:
        npi_patterns = [
            r'(?:npi|provider|national provider).*?(\d{10})',
            r'npi:\s*(\d{10})',
            r'\b(\d{10})\b'
        ]

        for line in lines:
            if any(word in line.lower() for word in ['npi', 'provider', 'national']):
                for pattern in npi_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        return match.group(1)

        for pattern in npi_patterns[:2]:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                return match.group(1)

        return ""

    def _find_address(self, lines: List[str], full_text: str) -> str:
        address_patterns = [
            r'(\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd|Circle|Cir|Court|Ct|Place|Pl)(?:\s*,?\s*(?:Suite|Ste|Unit|#)\s*\d+)?)',
            r'(\d+\s+[A-Za-z\s]+(?:Suite|Ste|Unit|#)\s*\d+)'
        ]

        for line in lines:
            if any(word in line.lower() for word in ['address', 'location', 'street']):
                for pattern in address_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        address = match.group(1).strip()
                        address = re.sub(r'\bSte\b', 'Suite', address, flags=re.IGNORECASE)
                        address = re.sub(r'\bUnit\b', 'Suite', address, flags=re.IGNORECASE)
                        address = re.sub(r'#(\d+)', r'Suite \1', address)
                        return address

        return ""

    def _find_city(self, lines: List[str], full_text: str) -> str:
        city_patterns = [
            r',\s*([A-Za-z\s]+),\s*[A-Z]{2}',
            r'city:\s*([A-Za-z\s]+)',
            r'([A-Za-z\s]+),\s*[A-Z]{2}\s*\d{5}'
        ]

        for pattern in city_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                city = match.group(1).strip()
                if len(city) > 2 and city.replace(' ', '').isalpha():
                    return city.title()

        return ""

    def _find_state(self, lines: List[str], full_text: str) -> str:
        state_patterns = [
            r',\s*([A-Z]{2})\s*\d{5}',
            r',\s*([A-Z]{2})\s*$',
            r'state:\s*([A-Z]{2})',
            r'\b([A-Z]{2})\s+\d{5}'
        ]

        for pattern in state_patterns:
            match = re.search(pattern, full_text, re.MULTILINE)
            if match:
                state = match.group(1).upper()
                if state in self.state_codes.values():
                    return state

        for state_name, code in self.state_codes.items():
            if state_name.lower() in full_text.lower():
                return code

        return ""

    def _find_icd_codes(self, lines: List[str], full_text: str) -> str:
        icd_patterns = [
            r'\b([A-Z]\d{2}\.?\d{0,3}[A-Z]?)\b',
            r'\b([A-Z]\d{2})\b',
            r'\b(\d{3}\.\d{2})\b'
        ]

        codes = set()

        for line in lines:
            if any(word in line.lower() for word in ['icd', 'diagnosis', 'diagnostic', 'code']):
                for pattern in icd_patterns:
                    matches = re.findall(pattern, line)
                    codes.update(matches)

        if not codes:
            for pattern in icd_patterns:
                matches = re.findall(pattern, full_text)
                for match in matches:
                    if re.match(r'^[A-Z]\d{2}', match):
                        codes.add(match)

        return ', '.join(sorted(list(codes))[:3]) if codes else ""

    def _find_modality(self, text_lower: str, full_text: str) -> str:
        modality_map = {
            'computed tomography': 'CT', 'ct scan': 'CT', 'cat scan': 'CT', 'ct': 'CT',
            'magnetic resonance': 'MRI', 'mri': 'MRI', 'mr': 'MRI',
            'x-ray': 'XR', 'xray': 'XR', 'radiograph': 'XR', 'xr': 'XR',
            'ultrasound': 'US', 'sonography': 'US', 'us': 'US', 'echo': 'US',
            'mammography': 'MM', 'mammogram': 'MM', 'mm': 'MM',
            'dxa': 'DXA', 'dexa': 'DXA', 'bone density': 'DXA'
        }

        for modality_text, code in modality_map.items():
            if modality_text in text_lower:
                return code

        modality_patterns = [
            r'\b(CT|MRI|XR|US|MM|DXA)\b',
            r'modality:\s*([A-Z]+)'
        ]

        for pattern in modality_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                modality = match.group(1).upper()
                if modality in ['CT', 'MRI', 'XR', 'US', 'MM', 'DXA']:
                    return modality

        return ""

    def _find_exam(self, lines: List[str], text_lower: str) -> str:
        exam_keywords = ['exam', 'study', 'procedure', 'imaging', 'scan']

        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in exam_keywords):
                if any(modality in line_lower for modality in ['ct', 'mri', 'xray', 'ultrasound', 'mammography']):
                    exam_text = line.strip()
                    if len(exam_text) > 10 and not any(exclude in exam_text.lower() for exclude in ['signature', 'form', 'field']):
                        return exam_text.title()

        exam_patterns = [
            r'(?:exam|study|procedure):\s*([A-Za-z\s]+(?:CT|MRI|XR|US|MM)[A-Za-z\s]*)',
            r'((?:CT|MRI|XR|US|MM)\s+[A-Za-z\s]+(?:with|without|w/|w/o)[A-Za-z\s]*)',
            r'([A-Za-z\s]*(?:CT|MRI|XR|US|MM)[A-Za-z\s]*(?:scan|study|exam)[A-Za-z\s]*)'
        ]

        full_text = '\n'.join(lines)
        for pattern in exam_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                exam = match.group(1).strip()
                if len(exam) > 5:
                    return exam.title()

        return ""

    def _find_body_part(self, text_lower: str, full_text: str) -> str:
        anatomical_parts = self.medical_terms['anatomical']
        directions = self.medical_terms['directions']

        found_parts = []
        found_directions = []

        for part in anatomical_parts:
            if part in text_lower:
                found_parts.append(part)

        for direction in directions:
            if f' {direction} ' in text_lower or f'{direction} ' in text_lower or f' {direction}' in text_lower:
                found_directions.append(direction)

        if found_parts:
            main_part = found_parts[0].title()

            if found_directions:
                direction = found_directions[0].lower()
                if direction in ['r', 'rt', 'right']:
                    return f"Right {main_part}"
                elif direction in ['l', 'lt', 'left']:
                    return f"Left {main_part}"
                elif direction in ['bilateral']:
                    return f"Bilateral {main_part}"

            return main_part

        body_part_patterns = [
            r'(?:body|part|region|area|anatomy):\s*([A-Za-z\s]+)',
            r'((?:right|left|bilateral)\s+[A-Za-z]+)',
            r'([A-Za-z]+\s+(?:right|left))'
        ]

        for pattern in body_part_patterns:
            match = re.search(pattern, full_text, re.IGNORECASE)
            if match:
                body_part = match.group(1).strip().title()
                if any(part in body_part.lower() for part in anatomical_parts):
                    return body_part

        return ""

    def _find_insurance_number(self, lines: List[str], full_text: str) -> str:
        insurance_patterns = [
            r'(?:insurance|policy|member|subscriber).*?([A-Z0-9]{6,})',
            r'(?:member|policy)\s*(?:id|number|#):\s*([A-Z0-9]+)',
            r'\b([A-Z]\d[A-Z]\w{6,})\b',
            r'\b(F\d[A-Z]\w{6,})\b'
        ]

        for line in lines:
            if any(word in line.lower() for word in ['insurance', 'policy', 'member', 'subscriber']):
                for pattern in insurance_patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        insurance_id = match.group(1)
                        if len(insurance_id) >= 6:
                            return insurance_id

        for pattern in insurance_patterns[2:]:
            match = re.search(pattern, full_text)
            if match:
                insurance_id = match.group(1)
                if len(insurance_id) >= 6:
                    return insurance_id

        return ""

    def _find_exam_codes(self, lines: List[str], full_text: str) -> str:
        code_patterns = [
            r'(?:cpt|procedure|billing).*?(\d{5})',
            r'code:\s*(\d{5})',
            r'\b(\d{5})\b'
        ]

        codes = set()

        for line in lines:
            if any(word in line.lower() for word in ['cpt', 'procedure', 'code', 'billing']):
                for pattern in code_patterns:
                    matches = re.findall(pattern, line)
                    codes.update(matches)

        if not codes:
            matches = re.findall(r'\b(\d{5})\b', full_text)
            for match in matches:
                if 70000 <= int(match) <= 79999:
                    codes.add(match)

        return ', '.join(sorted(list(codes))) if codes else ""

    def _post_process(self, results: Dict[str, str]) -> Dict[str, str]:
        if not results.get('fullname') and results.get('first_name') and results.get('last_name'):
            first = results['first_name']
            last = results['last_name']
            results['fullname'] = f"{last}, {first}"

        if results.get('fullname') and not results.get('first_name'):
            if ',' in results['fullname']:
                parts = results['fullname'].split(',')
                if len(parts) >= 2:
                    results['last_name'] = parts[0].strip()
                    name_parts = parts[1].strip().split()
                    if name_parts:
                        results['first_name'] = name_parts[0]

        return results
    
    def _find_last_name(self, lines: List[str], full_text: str) -> str:
        patient_indicators = ['patient', 'pt', 'name', 'last', 'family']
        exclude_words = ['signature', 'doctor', 'dr', 'physician', 'first', 'given', 'form', 'field']
        
        for line in lines:
            line_lower = line.lower()
            if any(indicator in line_lower for indicator in patient_indicators):
                words = re.findall(r'\b[A-Z][a-z]{2,}\b', line)
                if len(words) >= 2:
                    for word in words[1:]:
                        if word.lower() not in exclude_words and len(word) > 2:
                            return word
        
        name_patterns = [
            r'Last:\s*([A-Z][a-z]+)',
            r'Family:\s*([A-Z][a-z]+)',
            r'Surname:\s*([A-Z][a-z]+)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, full_text)
            if match:
                name = match.group(1)
                if name.lower() not in exclude_words:
                    return name
        
        return ""
    
    def _find_full_name(self, lines: List[str], full_text: str) -> str:
        exclude_words = ['signature', 'doctor', 'dr', 'physician', 'form', 'field']
        
        name_patterns = [
            r'Patient:\s*([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
            r'Name:\s*([A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
            r'([A-Z][a-z]+,\s*[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, full_text)
            if match:
                name = match.group(1)
                if not any(exclude in name.lower() for exclude in exclude_words):
                    if ',' in name:
                        return name
                    else:
                        parts = name.split()
                        if len(parts) >= 2:
                            return f"{parts[-1]}, {' '.join(parts[:-1])}"
        
        for line in lines:
            if 'patient' in line.lower() and 'name' in line.lower():
                words = re.findall(r'\b[A-Z][a-z]{2,}\b', line)
                name_words = [w for w in words if w.lower() not in exclude_words]
                if len(name_words) >= 2:
                    return f"{name_words[-1]}, {' '.join(name_words[:-1])}"
        
        return ""
