import json
import boto3
import os
import logging
import re
import sys
from typing import Dict, Any, List
from datetime import datetime

sys.path.append('/opt/python')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

logger = logging.getLogger()
logger.setLevel(logging.INFO)

textract = boto3.client('textract')
bedrock = boto3.client('bedrock-runtime')
s3 = boto3.client('s3')

INPUT_BUCKET = 'data-automation-dev-bucket'
OUTPUT_BUCKET = os.environ.get('OUTPUT_BUCKET', 'medical-pdf-textract-results')

def lambda_handler(event, context):
    try:
        bucket = event['Records'][0]['s3']['bucket']['name']
        key = event['Records'][0]['s3']['object']['key']
        
        logger.info(f"Processing file: s3://{bucket}/{key}")
        
        extracted_text = extract_text_from_pdf(bucket, key)
        medical_fields = extract_fields_with_ai(extracted_text)
        formatted_results = format_medical_data(medical_fields)
        
        result_key = f"textract_results/{key.replace('.pdf', '_intelligent_results.json')}"
        save_results_to_s3(formatted_results, OUTPUT_BUCKET, result_key)
        
        return {
            'statusCode': 200,
            'body': json.dumps({
                'message': 'Successfully processed medical PDF with intelligent extraction',
                'input_file': f"s3://{bucket}/{key}",
                'output_file': f"s3://{OUTPUT_BUCKET}/{result_key}",
                'extracted_fields': list(formatted_results.keys()),
                'processing_method': 'intelligent_ai_extraction'
            })
        }
        
    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': str(e),
                'message': 'Failed to process medical PDF'
            })
        }

def extract_text_from_pdf(bucket: str, key: str) -> str:
    try:
        logger.info(f"Starting Textract extraction for s3://{bucket}/{key}")
        
        try:
            response = textract.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            extracted_text = ""
            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    extracted_text += block['Text'] + "\n"
            
            logger.info(f"Synchronous extraction: {len(extracted_text)} characters")
            return extracted_text
            
        except Exception as sync_error:
            logger.info(f"Synchronous extraction failed, trying asynchronous: {str(sync_error)}")
            
            response = textract.start_document_text_detection(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            job_id = response['JobId']
            logger.info(f"Started async Textract job: {job_id}")
            
            import time
            max_wait = 300
            wait_time = 0
            
            while wait_time < max_wait:
                response = textract.get_document_text_detection(JobId=job_id)
                status = response['JobStatus']
                
                if status == 'SUCCEEDED':
                    break
                elif status == 'FAILED':
                    raise Exception(f"Textract job failed: {response.get('StatusMessage', 'Unknown error')}")
                
                time.sleep(5)
                wait_time += 5
            
            if wait_time >= max_wait:
                raise Exception("Textract job timeout")
            
            extracted_text = ""
            next_token = None
            
            while True:
                if next_token:
                    response = textract.get_document_text_detection(
                        JobId=job_id,
                        NextToken=next_token
                    )
                else:
                    response = textract.get_document_text_detection(JobId=job_id)
                
                for block in response['Blocks']:
                    if block['BlockType'] == 'LINE':
                        extracted_text += block['Text'] + "\n"
                
                next_token = response.get('NextToken')
                if not next_token:
                    break
            
            logger.info(f"Async extraction: {len(extracted_text)} characters")
            return extracted_text
        
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        raise

def extract_fields_with_ai(text: str) -> Dict[str, str]:
    try:
        logger.info("Using AI-powered field extraction")
        
        prompt = create_extraction_prompt(text)
        
        try:
            response = bedrock.invoke_model(
                modelId='anthropic.claude-3-sonnet-20240229-v1:0',
                body=json.dumps({
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 4000,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                })
            )
            
            response_body = json.loads(response['body'].read())
            ai_response = response_body['content'][0]['text']
            
            extracted_fields = parse_ai_response(ai_response)
            logger.info(f"AI extracted {len(extracted_fields)} fields")
            
            return extracted_fields
            
        except Exception as bedrock_error:
            logger.warning(f"Bedrock failed, using intelligent rule-based extraction: {str(bedrock_error)}")
            return extract_fields_intelligent(text)
        
    except Exception as e:
        logger.error(f"Error in AI field extraction: {str(e)}")
        return extract_fields_intelligent(text)

def create_extraction_prompt(text: str) -> str:
    return f"""
You are an expert medical document processor. Extract the following fields from this medical document text. 
Return ONLY a JSON object with the field names as keys and extracted values as values. 
If a field is not found, use an empty string "".

Required fields:
- patient_phone_number: Patient's phone number
- doctor_phone_number: Ordering/referring physician's phone number  
- doctor_fax: Physician's fax number
- first_name: Patient's first name only
- last_name: Patient's last name only
- fullname: Patient's complete name
- birthDate: Patient's date of birth
- gender: Patient's gender (Male/Female)
- doctor_X: Ordering/referring physician's name
- doctor_NPI: Physician's NPI number (10 digits)
- doctor_address: Physician's facility address
- doctor_city: Physician's facility city
- doctor_state: Physician's facility state
- icd_codes: ICD diagnostic codes
- modality: Imaging modality (CT, MRI, XR, US, MM)
- exam: Complete examination description
- bodyPart: Anatomical region being examined
- insurance_number: Patient's insurance ID
- doctor_exam_codes: CPT/procedure codes

Medical document text:
{text[:8000]}

Return only the JSON object:
"""

def parse_ai_response(ai_response: str) -> Dict[str, str]:
    try:
        json_start = ai_response.find('{')
        json_end = ai_response.rfind('}') + 1
        
        if json_start != -1 and json_end != -1:
            json_str = ai_response[json_start:json_end]
            return json.loads(json_str)
        else:
            logger.warning("Could not find JSON in AI response")
            return {}
            
    except Exception as e:
        logger.error(f"Error parsing AI response: {str(e)}")
        return {}

def extract_fields_intelligent(text: str) -> Dict[str, str]:
    logger.info("Using intelligent rule-based extraction")
    
    extractor = IntelligentExtractor()
    return extractor.extract_all_fields(text)

class IntelligentExtractor:
    def __init__(self):
        self.medical_terms = {
            'anatomical': ['head', 'brain', 'neck', 'chest', 'lung', 'heart', 'abdomen', 'pelvis', 'spine', 'shoulder', 'knee', 'ankle'],
            'modalities': ['ct', 'mri', 'xray', 'x-ray', 'ultrasound', 'mammography'],
            'directions': ['right', 'left', 'bilateral', 'r', 'l', 'rt', 'lt']
        }
    
    def extract_all_fields(self, text: str) -> Dict[str, str]:
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        text_lower = text.lower()
        
        results = {}
        
        results['patient_phone_number'] = self._find_phone_number(lines, 'patient')
        results['doctor_phone_number'] = self._find_phone_number(lines, 'doctor')
        results['doctor_fax'] = self._find_fax_number(lines)
        results['first_name'] = self._find_first_name(lines)
        results['last_name'] = self._find_last_name(lines)
        results['fullname'] = self._find_full_name(lines)
        results['birthDate'] = self._find_birth_date(lines)
        results['gender'] = self._find_gender(lines)
        results['doctor_X'] = self._find_doctor_name(lines)
        results['doctor_NPI'] = self._find_npi(lines)
        results['doctor_address'] = self._find_address(lines)
        results['doctor_city'] = self._find_city(lines)
        results['doctor_state'] = self._find_state(lines)
        results['icd_codes'] = self._find_icd_codes(lines)
        results['modality'] = self._find_modality(text_lower)
        results['exam'] = self._find_exam(lines, text_lower)
        results['bodyPart'] = self._find_body_part(text_lower)
        results['insurance_number'] = self._find_insurance_number(lines)
        results['doctor_exam_codes'] = self._find_exam_codes(lines)
        
        return self._post_process(results)
    
    def _find_phone_number(self, lines: List[str], context: str) -> str:
        phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
        
        for line in lines:
            if context.lower() in line.lower():
                matches = re.findall(phone_pattern, line)
                if matches:
                    return self._format_phone(matches[0])
        
        for line in lines:
            matches = re.findall(phone_pattern, line)
            if matches and context == 'patient' and 'doctor' not in line.lower():
                return self._format_phone(matches[0])
            elif matches and context == 'doctor' and any(word in line.lower() for word in ['doctor', 'dr', 'physician']):
                return self._format_phone(matches[0])
        
        return ""
    
    def _find_fax_number(self, lines: List[str]) -> str:
        phone_pattern = r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
        
        for line in lines:
            if 'fax' in line.lower():
                matches = re.findall(phone_pattern, line)
                if matches:
                    return self._format_phone(matches[0])
        
        return ""
    
    def _format_phone(self, phone: str) -> str:
        digits = re.sub(r'[^\d]', '', phone)
        if len(digits) == 10:
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits.startswith('1'):
            digits = digits[1:]
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        return phone
    
    def _find_first_name(self, lines: List[str]) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['patient', 'first', 'given']):
                words = re.findall(r'\b[A-Z][a-z]+\b', line)
                for word in words:
                    if word.lower() not in ['patient', 'first', 'given', 'name', 'last']:
                        return word
        return ""
    
    def _find_last_name(self, lines: List[str]) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['patient', 'last', 'family', 'surname']):
                words = re.findall(r'\b[A-Z][a-z]+\b', line)
                for word in words:
                    if word.lower() not in ['patient', 'last', 'family', 'surname', 'name', 'first']:
                        return word
        return ""
    
    def _find_full_name(self, lines: List[str]) -> str:
        for line in lines:
            if 'patient' in line.lower() and 'name' in line.lower():
                words = re.findall(r'\b[A-Z][a-z]+\b', line)
                name_words = [w for w in words if w.lower() not in ['patient', 'name', 'first', 'last']]
                if len(name_words) >= 2:
                    return f"{name_words[-1]}, {' '.join(name_words[:-1])}"
        return ""
    
    def _find_birth_date(self, lines: List[str]) -> str:
        date_pattern = r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})'
        
        for line in lines:
            if any(word in line.lower() for word in ['dob', 'birth', 'born']):
                match = re.search(date_pattern, line)
                if match:
                    day, month, year = match.groups()
                    if len(year) == 2:
                        year = f"20{year}" if int(year) < 50 else f"19{year}"
                    return f"{day.zfill(2)}/{month.zfill(2)}/{year}"
        return ""
    
    def _find_gender(self, lines: List[str]) -> str:
        for line in lines:
            line_lower = line.lower()
            if 'gender' in line_lower or 'sex' in line_lower:
                if 'male' in line_lower and 'female' not in line_lower:
                    return 'Male'
                elif 'female' in line_lower:
                    return 'Female'
                elif re.search(r'\bm\b', line_lower):
                    return 'Male'
                elif re.search(r'\bf\b', line_lower):
                    return 'Female'
        return ""
    
    def _find_doctor_name(self, lines: List[str]) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['doctor', 'dr', 'physician', 'referring', 'ordering']):
                line_clean = re.sub(r'\b(?:dr\.?|doctor|md|do)\b', '', line, flags=re.IGNORECASE)
                words = re.findall(r'\b[A-Z][a-z]+\b', line_clean)
                name_words = [w for w in words if w.lower() not in ['referring', 'ordering', 'physician']]
                if len(name_words) >= 2:
                    return ' '.join(name_words[:3])
        return ""
    
    def _find_npi(self, lines: List[str]) -> str:
        for line in lines:
            if 'npi' in line.lower():
                npi_match = re.search(r'\b(\d{10})\b', line)
                if npi_match:
                    return npi_match.group(1)
        return ""
    
    def _find_address(self, lines: List[str]) -> str:
        address_pattern = r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln)'
        
        for line in lines:
            if 'address' in line.lower():
                match = re.search(address_pattern, line, re.IGNORECASE)
                if match:
                    return match.group(0).strip()
        return ""
    
    def _find_city(self, lines: List[str]) -> str:
        for line in lines:
            city_match = re.search(r',\s*([A-Za-z\s]+),\s*[A-Z]{2}', line)
            if city_match:
                return city_match.group(1).strip().title()
        return ""
    
    def _find_state(self, lines: List[str]) -> str:
        state_codes = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
            'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
            'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
            'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
            'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
            'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
            'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
            'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
        }
        
        for line in lines:
            state_match = re.search(r'\b([A-Z]{2})\b', line)
            if state_match:
                return state_match.group(1)
            
            for state_name, code in state_codes.items():
                if state_name.lower() in line.lower():
                    return code
        return ""
    
    def _find_icd_codes(self, lines: List[str]) -> str:
        codes = []
        for line in lines:
            if any(word in line.lower() for word in ['icd', 'diagnosis']):
                icd_matches = re.findall(r'\b([A-Z]\d{2}\.?\d{0,2})\b', line)
                codes.extend(icd_matches)
        return ', '.join(codes[:3]) if codes else ""
    
    def _find_modality(self, text_lower: str) -> str:
        modality_map = {
            'computed tomography': 'CT', 'ct scan': 'CT', 'cat scan': 'CT',
            'magnetic resonance': 'MRI', 'mri': 'MRI',
            'x-ray': 'XR', 'xray': 'XR', 'radiograph': 'XR',
            'ultrasound': 'US', 'sonography': 'US',
            'mammography': 'MM', 'mammogram': 'MM'
        }
        
        for modality, code in modality_map.items():
            if modality in text_lower:
                return code
        return ""
    
    def _find_exam(self, lines: List[str], text_lower: str) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['exam', 'study', 'procedure']):
                if any(mod in line.lower() for mod in ['ct', 'mri', 'xray', 'ultrasound']):
                    return line.strip().title()
        return ""
    
    def _find_body_part(self, text_lower: str) -> str:
        for part in self.medical_terms['anatomical']:
            if part in text_lower:
                result = part.title()
                for direction in self.medical_terms['directions']:
                    if direction in text_lower:
                        if direction.lower() in ['r', 'rt', 'right']:
                            result = f"Right {result}"
                        elif direction.lower() in ['l', 'lt', 'left']:
                            result = f"Left {result}"
                        break
                return result
        return ""
    
    def _find_insurance_number(self, lines: List[str]) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['insurance', 'policy', 'member']):
                insurance_match = re.search(r'\b([A-Z0-9]{6,})\b', line)
                if insurance_match:
                    return insurance_match.group(1)
        return ""
    
    def _find_exam_codes(self, lines: List[str]) -> str:
        codes = []
        for line in lines:
            if any(word in line.lower() for word in ['cpt', 'procedure', 'code']):
                code_matches = re.findall(r'\b(\d{5})\b', line)
                codes.extend(code_matches)
        return ', '.join(codes) if codes else ""
    
    def _post_process(self, results: Dict[str, str]) -> Dict[str, str]:
        if not results.get('fullname') and results.get('first_name') and results.get('last_name'):
            results['fullname'] = f"{results['last_name']}, {results['first_name']}"
        
        if results.get('fullname') and not results.get('first_name'):
            if ',' in results['fullname']:
                parts = results['fullname'].split(',')
                if len(parts) >= 2:
                    results['last_name'] = parts[0].strip()
                    results['first_name'] = parts[1].strip().split()[0]
        
        return results

def format_medical_data(raw_fields: Dict[str, Any]) -> Dict[str, str]:
    formatted = {}
    
    for field, value in raw_fields.items():
        if not value:
            formatted[field] = ""
            continue
            
        value_str = str(value).strip()
        formatted[field] = value_str
    
    return formatted

def save_results_to_s3(results: Dict[str, str], bucket: str, key: str):
    try:
        result_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'processing_method': 'intelligent_ai_extraction',
            'source_bucket': INPUT_BUCKET,
            'extracted_fields': results,
            'field_count': len([v for v in results.values() if v]),
            'total_fields': len(results)
        }
        
        s3.put_object(
            Bucket=bucket,
            Key=key,
            Body=json.dumps(result_data, indent=2),
            ContentType='application/json'
        )
        
        logger.info(f"Results saved to s3://{bucket}/{key}")
        
    except Exception as e:
        logger.error(f"Error saving results to S3: {str(e)}")
        raise
