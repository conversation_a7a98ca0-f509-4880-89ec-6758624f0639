import sys
import os
import boto3
import time
import re
import json
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter
import random

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
from ground_truth_data import GROUND_TRUTH_DATA

class TrainedMedicalExtractor:
    def __init__(self):
        self.field_patterns = {}
        self.context_patterns = {}
        self.value_mappings = {}
        self.trained = False
        
    def train_from_ground_truth(self, training_files: List[str]):
        """Train the extractor using ground truth data"""
        print(f"Training on {len(training_files)} files...")
        
        # Extract text for all training files
        textract = boto3.client('textract', region_name='us-east-1')
        training_data = []
        
        for filename in training_files:
            if filename in GROUND_TRUTH_DATA:
                print(f"Processing training file: {filename}")
                file_key = f"Imaging Orders OCR Data/{filename}"
                extracted_text = self._extract_text_with_textract(textract, "data-automation-dev-bucket", file_key)
                
                if extracted_text:
                    training_data.append({
                        'filename': filename,
                        'text': extracted_text,
                        'ground_truth': GROUND_TRUTH_DATA[filename]
                    })
        
        print(f"Successfully extracted text from {len(training_data)} training files")
        
        # Learn patterns from training data
        self._learn_patterns(training_data)
        self.trained = True
        print("Training completed!")
        
    def _learn_patterns(self, training_data: List[Dict]):
        """Learn extraction patterns from training data"""
        
        # Initialize pattern storage
        field_contexts = defaultdict(list)
        field_values = defaultdict(list)
        
        for data in training_data:
            text = data['text']
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            ground_truth = data['ground_truth']
            
            # For each field, find where it appears in the text
            for field_name, true_value in ground_truth.items():
                if true_value:  # Only learn from non-empty values
                    context = self._find_value_context(lines, text, true_value, field_name)
                    if context:
                        field_contexts[field_name].append(context)
                        field_values[field_name].append(true_value)
        
        # Generate patterns for each field
        for field_name in field_contexts:
            self.field_patterns[field_name] = self._generate_patterns(field_contexts[field_name], field_values[field_name], field_name)
        
        print(f"Learned patterns for {len(self.field_patterns)} fields")
        
    def _find_value_context(self, lines: List[str], full_text: str, true_value: str, field_name: str) -> Dict:
        """Find the context where a value appears in the text"""
        
        # Clean the true value for searching
        search_value = str(true_value).strip()
        
        # Special handling for different field types
        if field_name == 'birthDate':
            # Try different date formats
            date_variants = self._generate_date_variants(search_value)
            for variant in date_variants:
                context = self._find_text_context(lines, variant)
                if context:
                    return context
        
        elif field_name in ['patient_phone_number', 'doctor_phone_number', 'doctor_fax']:
            # Try different phone formats
            phone_variants = self._generate_phone_variants(search_value)
            for variant in phone_variants:
                context = self._find_text_context(lines, variant)
                if context:
                    return context
        
        elif field_name in ['first_name', 'last_name']:
            # Look for name in various contexts
            context = self._find_name_context(lines, search_value, field_name)
            if context:
                return context
        
        else:
            # Direct text search
            context = self._find_text_context(lines, search_value)
            if context:
                return context
        
        return None
    
    def _find_text_context(self, lines: List[str], search_value: str) -> Dict:
        """Find the context around a specific value in the text"""
        
        for i, line in enumerate(lines):
            if search_value.lower() in line.lower():
                return {
                    'line': line,
                    'line_index': i,
                    'before_lines': lines[max(0, i-2):i],
                    'after_lines': lines[i+1:min(len(lines), i+3)],
                    'value': search_value,
                    'line_position': line.lower().find(search_value.lower())
                }
        
        return None
    
    def _find_name_context(self, lines: List[str], name: str, field_type: str) -> Dict:
        """Find name context with better matching"""
        
        # Try exact match first
        context = self._find_text_context(lines, name)
        if context:
            return context
        
        # Try partial matches for names
        name_words = name.split()
        for word in name_words:
            if len(word) > 2:
                context = self._find_text_context(lines, word)
                if context:
                    return context
        
        return None
    
    def _generate_date_variants(self, date_str: str) -> List[str]:
        """Generate different date format variants"""
        variants = [date_str]
        
        # Try to parse and convert between formats
        date_patterns = [
            r'(\d{1,2})/(\d{1,2})/(\d{4})',
            r'(\d{1,2})-(\d{1,2})-(\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                d1, d2, year = match.groups()
                # Add both DD/MM and MM/DD variants
                variants.extend([
                    f"{d1}/{d2}/{year}",
                    f"{d2}/{d1}/{year}",
                    f"{d1}-{d2}-{year}",
                    f"{d2}-{d1}-{year}",
                    f"{d1.zfill(2)}/{d2.zfill(2)}/{year}",
                    f"{d2.zfill(2)}/{d1.zfill(2)}/{year}"
                ])
        
        return list(set(variants))
    
    def _generate_phone_variants(self, phone_str: str) -> List[str]:
        """Generate different phone format variants"""
        variants = [phone_str]
        
        # Extract digits
        digits = ''.join(filter(str.isdigit, phone_str))
        if len(digits) >= 10:
            # Generate common formats
            variants.extend([
                f"{digits[:3]}-{digits[3:6]}-{digits[6:10]}",
                f"({digits[:3]}) {digits[3:6]}-{digits[6:10]}",
                f"{digits[:3]}.{digits[3:6]}.{digits[6:10]}",
                f"{digits[:3]} {digits[3:6]} {digits[6:10]}",
                digits[:10]
            ])
        
        return list(set(variants))
    
    def _generate_patterns(self, contexts: List[Dict], values: List[str], field_name: str) -> Dict:
        """Generate extraction patterns from contexts"""
        
        patterns = {
            'keywords': [],
            'line_patterns': [],
            'context_patterns': [],
            'value_patterns': []
        }
        
        # Extract common keywords from contexts
        all_words = []
        for context in contexts:
            line_words = re.findall(r'\b\w+\b', context['line'].lower())
            all_words.extend(line_words)
        
        # Find most common keywords (excluding the values themselves)
        value_words = set()
        for value in values:
            value_words.update(re.findall(r'\b\w+\b', str(value).lower()))
        
        word_counts = Counter(all_words)
        common_keywords = [word for word, count in word_counts.most_common(10) 
                          if word not in value_words and len(word) > 2]
        patterns['keywords'] = common_keywords[:5]
        
        # Generate line patterns
        for context in contexts:
            line = context['line']
            value = context['value']
            
            # Create pattern by replacing the value with a placeholder
            pattern = line.replace(value, '<VALUE>')
            patterns['line_patterns'].append(pattern)
        
        # Generate context patterns (words that appear before/after)
        before_words = []
        after_words = []
        
        for context in contexts:
            line = context['line'].lower()
            value_pos = context['line_position']
            
            before_text = line[:value_pos]
            after_text = line[value_pos + len(context['value']):]
            
            before_words.extend(re.findall(r'\b\w+\b', before_text)[-3:])
            after_words.extend(re.findall(r'\b\w+\b', after_text)[:3])
        
        patterns['context_patterns'] = {
            'before': list(set(before_words)),
            'after': list(set(after_words))
        }
        
        return patterns
    
    def extract_all_fields(self, text: str) -> Dict[str, str]:
        """Extract fields using learned patterns"""
        
        if not self.trained:
            raise ValueError("Extractor must be trained before use")
        
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        results = {}
        
        for field_name in GROUND_TRUTH_DATA[list(GROUND_TRUTH_DATA.keys())[0]].keys():
            if field_name in self.field_patterns:
                extracted_value = self._extract_field_with_patterns(lines, text, field_name)
                results[field_name] = extracted_value
            else:
                results[field_name] = ""
        
        return results
    
    def _extract_field_with_patterns(self, lines: List[str], full_text: str, field_name: str) -> str:
        """Extract a specific field using learned patterns"""
        
        patterns = self.field_patterns[field_name]
        
        # Try keyword-based extraction first
        candidate_lines = []
        for line in lines:
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in patterns['keywords']):
                candidate_lines.append(line)
        
        # If no keyword matches, use all lines
        if not candidate_lines:
            candidate_lines = lines
        
        # Apply field-specific extraction
        if field_name == 'birthDate':
            return self._extract_date_with_patterns(candidate_lines, patterns)
        elif field_name in ['patient_phone_number', 'doctor_phone_number', 'doctor_fax']:
            return self._extract_phone_with_patterns(candidate_lines, patterns, field_name)
        elif field_name in ['first_name', 'last_name', 'fullname']:
            return self._extract_name_with_patterns(candidate_lines, patterns, field_name)
        elif field_name == 'modality':
            return self._extract_modality_with_patterns(candidate_lines, full_text, patterns)
        elif field_name == 'icd_codes':
            return self._extract_icd_with_patterns(candidate_lines, patterns)
        elif field_name == 'doctor_NPI':
            return self._extract_npi_with_patterns(candidate_lines, patterns)
        elif field_name == 'doctor_state':
            return self._extract_state_with_patterns(candidate_lines, patterns)
        else:
            return self._extract_generic_with_patterns(candidate_lines, patterns)
    
    def _extract_date_with_patterns(self, lines: List[str], patterns: Dict) -> str:
        """Extract date using learned patterns"""
        
        date_patterns = [
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})'
        ]
        
        for line in lines:
            for pattern in date_patterns:
                match = re.search(pattern, line)
                if match:
                    parts = match.groups()
                    if len(parts[2]) == 4:
                        month, day, year = parts
                    else:
                        month, day, year = parts
                        year = f"20{year}" if int(year) < 50 else f"19{year}"
                    
                    # Convert to DD/MM/YYYY format
                    return f"{day.zfill(2)}/{month.zfill(2)}/{year}"
        
        return ""

    def _extract_phone_with_patterns(self, lines: List[str], patterns: Dict, field_type: str) -> str:
        """Extract phone number using learned patterns"""

        phone_patterns = [
            r'\((\d{3})\)\s*(\d{3})[-\s]*(\d{4})',
            r'(\d{3})[-\.\s](\d{3})[-\.\s](\d{4})'
        ]

        # Use context patterns to find the right phone number
        context_before = patterns['context_patterns']['before']
        context_after = patterns['context_patterns']['after']

        for line in lines:
            line_lower = line.lower()

            # Check if line has relevant context
            has_context = any(word in line_lower for word in context_before + context_after)

            if has_context or field_type in ['doctor_fax'] and 'fax' in line_lower:
                for pattern in phone_patterns:
                    match = re.search(pattern, line)
                    if match:
                        if len(match.groups()) == 3:
                            return f"{match.group(1)}-{match.group(2)}-{match.group(3)}"
                        else:
                            digits = re.sub(r'[^\d]', '', match.group(0))
                            if len(digits) == 10:
                                return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"

        return ""

    def _extract_name_with_patterns(self, lines: List[str], patterns: Dict, field_type: str) -> str:
        """Extract name using learned patterns"""

        context_keywords = patterns['keywords']

        for line in lines:
            line_lower = line.lower()

            # Check if line has name-related context
            if any(keyword in line_lower for keyword in context_keywords):
                # Extract capitalized words (likely names)
                words = re.findall(r'\b[A-Z][a-z]{2,}\b', line)

                # Filter out common non-name words
                exclude_words = ['Patient', 'Name', 'First', 'Last', 'Doctor', 'From', 'To', 'Date', 'Phone', 'Address']
                name_words = [w for w in words if w not in exclude_words]

                if name_words:
                    if field_type == 'first_name':
                        return name_words[0]
                    elif field_type == 'last_name':
                        return name_words[-1] if len(name_words) > 1 else name_words[0]
                    elif field_type == 'fullname':
                        if len(name_words) >= 2:
                            return f"{name_words[-1]}, {' '.join(name_words[:-1])}"

        return ""

    def _extract_modality_with_patterns(self, lines: List[str], full_text: str, patterns: Dict) -> str:
        """Extract modality using learned patterns"""

        modality_map = {
            'ct': 'CT', 'computed tomography': 'CT', 'cat': 'CT',
            'mri': 'MRI', 'magnetic resonance': 'MRI',
            'xr': 'XR', 'x-ray': 'XR', 'xray': 'XR',
            'us': 'US', 'ultrasound': 'US', 'sonography': 'US',
            'mm': 'MM', 'mammography': 'MM', 'mammogram': 'MM',
            'dxa': 'DXA', 'dexa': 'DXA'
        }

        full_text_lower = full_text.lower()

        # Check for modality keywords in context
        for modality_text, code in modality_map.items():
            if modality_text in full_text_lower:
                return code

        return ""

    def _extract_icd_with_patterns(self, lines: List[str], patterns: Dict) -> str:
        """Extract ICD codes using learned patterns"""

        icd_patterns = [
            r'\b([A-Z]\d{2}\.?\d{0,3}[A-Z]?)\b',
            r'\b([A-Z]\d{2})\b'
        ]

        codes = set()
        context_keywords = patterns['keywords']

        for line in lines:
            line_lower = line.lower()

            # Look for ICD codes in relevant context
            if any(keyword in line_lower for keyword in context_keywords) or any(word in line_lower for word in ['icd', 'diagnosis', 'code']):
                for pattern in icd_patterns:
                    matches = re.findall(pattern, line)
                    codes.update(matches)

        return ', '.join(sorted(list(codes))[:3]) if codes else ""

    def _extract_npi_with_patterns(self, lines: List[str], patterns: Dict) -> str:
        """Extract NPI using learned patterns"""

        npi_pattern = r'\b(\d{10})\b'
        context_keywords = patterns['keywords']

        for line in lines:
            line_lower = line.lower()

            if any(keyword in line_lower for keyword in context_keywords) or 'npi' in line_lower:
                match = re.search(npi_pattern, line)
                if match:
                    return match.group(1)

        return ""

    def _extract_state_with_patterns(self, lines: List[str], patterns: Dict) -> str:
        """Extract state using learned patterns"""

        state_codes = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
            'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
            'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
            'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
            'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
            'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
            'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
            'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
        }

        full_text = '\n'.join(lines)

        # Look for state codes
        state_pattern = r'\b([A-Z]{2})\b'
        matches = re.findall(state_pattern, full_text)

        for match in matches:
            if match in state_codes.values():
                return match

        # Look for full state names
        for state_name, code in state_codes.items():
            if state_name.lower() in full_text.lower():
                return code

        return ""

    def _extract_generic_with_patterns(self, lines: List[str], patterns: Dict) -> str:
        """Generic extraction using learned patterns"""

        context_keywords = patterns['keywords']

        for line in lines:
            line_lower = line.lower()

            if any(keyword in line_lower for keyword in context_keywords):
                # Extract meaningful text from the line
                words = re.findall(r'\b[A-Za-z0-9]+\b', line)
                if words:
                    return ' '.join(words[:5])  # Return first few words

        return ""

    def _extract_text_with_textract(self, textract, bucket, key):
        """Extract text using Textract"""
        try:
            try:
                response = textract.detect_document_text(
                    Document={
                        'S3Object': {
                            'Bucket': bucket,
                            'Name': key
                        }
                    }
                )

                extracted_text = ""
                for block in response['Blocks']:
                    if block['BlockType'] == 'LINE':
                        extracted_text += block['Text'] + "\n"

                return extracted_text

            except Exception:
                response = textract.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket,
                            'Name': key
                        }
                    }
                )

                job_id = response['JobId']

                max_wait = 120
                wait_time = 0

                while wait_time < max_wait:
                    response = textract.get_document_text_detection(JobId=job_id)
                    status = response['JobStatus']

                    if status == 'SUCCEEDED':
                        break
                    elif status == 'FAILED':
                        raise Exception(f"Textract job failed")

                    time.sleep(3)
                    wait_time += 3

                if wait_time >= max_wait:
                    raise Exception("Textract timeout")

                extracted_text = ""
                next_token = None

                while True:
                    if next_token:
                        response = textract.get_document_text_detection(
                            JobId=job_id,
                            NextToken=next_token
                        )
                    else:
                        response = textract.get_document_text_detection(JobId=job_id)

                    for block in response['Blocks']:
                        if block['BlockType'] == 'LINE':
                            extracted_text += block['Text'] + "\n"

                    next_token = response.get('NextToken')
                    if not next_token:
                        break

                return extracted_text

        except Exception as e:
            print(f"Textract error: {str(e)}")
            return None
