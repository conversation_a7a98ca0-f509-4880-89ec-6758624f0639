import sys
import os
from difflib import SequenceMatcher

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ground_truth_data import GROUND_TRUTH_DATA
from llm_extractor import LLMBasedExtractor

def calculate_similarity(extracted, ground_truth):
    """Calculate similarity between extracted and ground truth values with lenient evaluation"""
    if not extracted and not ground_truth:
        return 1.0
    if not extracted or not ground_truth:
        return 0.0
    
    extracted_clean = str(extracted).strip().lower()
    ground_truth_clean = str(ground_truth).strip().lower()
    
    # Special handling for phone numbers - consider same digits as match
    if any(char.isdigit() for char in str(extracted)) and any(char.isdigit() for char in str(ground_truth)):
        extracted_digits = ''.join(filter(str.isdigit, extracted_clean))
        truth_digits = ''.join(filter(str.isdigit, ground_truth_clean))
        if extracted_digits and truth_digits and len(extracted_digits) >= 10 and len(truth_digits) >= 10:
            return 1.0 if extracted_digits == truth_digits else 0.0
    
    # Special handling for body parts - lenient matching
    if any(word in ground_truth_clean for word in ['shoulder', 'spine', 'brain', 'chest', 'abdomen', 'knee', 'ankle']):
        if any(word in extracted_clean for word in ground_truth_clean.split()):
            return 1.0
    
    # Use sequence matcher for similarity
    similarity = SequenceMatcher(None, extracted_clean, ground_truth_clean).ratio()
    
    # Consider 80%+ similarity as a match for lenient evaluation
    return 1.0 if similarity >= 0.8 else similarity

def test_llm_extractor():
    print("TESTING LLM-BASED MEDICAL FIELD EXTRACTOR")
    print("=" * 70)
    print("Approach: Textract Raw Text → LLM Intelligence → Formatted Fields")
    print("=" * 70)
    
    # Test files
    test_files = [
        "531087480.pdf",
        "531082057.pdf", 
        "531107444.pdf",
        "531146293.pdf",
        "531191234.pdf"
    ]
    
    extractor = LLMBasedExtractor()
    
    overall_results = []
    field_accuracy = {}
    
    for i, filename in enumerate(test_files, 1):
        print(f"\nTEST {i}/5: {filename}")
        print("-" * 50)
        
        if filename not in GROUND_TRUTH_DATA:
            print(f"No ground truth data for {filename}")
            continue
        
        ground_truth = GROUND_TRUTH_DATA[filename]
        
        try:
            # Extract fields using LLM-based approach
            file_key = f"Imaging Orders OCR Data/{filename}"
            extracted_fields = extractor.extract_fields_from_pdf("data-automation-dev-bucket", file_key)
            
            if not extracted_fields:
                print("Failed to extract fields")
                continue
            
            # Calculate accuracy for each field
            field_scores = {}
            total_score = 0
            total_fields = 0
            
            for field_name in ground_truth.keys():
                extracted_value = extracted_fields.get(field_name, "")
                truth_value = ground_truth[field_name]
                
                similarity = calculate_similarity(extracted_value, truth_value)
                field_scores[field_name] = {
                    'extracted': extracted_value,
                    'ground_truth': truth_value,
                    'similarity': similarity,
                    'match': similarity >= 0.8
                }
                
                total_score += similarity
                total_fields += 1
                
                # Track field-level accuracy
                if field_name not in field_accuracy:
                    field_accuracy[field_name] = []
                field_accuracy[field_name].append(similarity)
            
            file_accuracy = (total_score / total_fields) * 100 if total_fields > 0 else 0
            
            print(f"Overall accuracy: {file_accuracy:.1f}%")
            print(f"Fields analyzed: {total_fields}")
            
            # Show top matches and improvements
            matches = [f for f, s in field_scores.items() if s['match']]
            misses = [f for f, s in field_scores.items() if not s['match']]
            
            print(f"✅ MATCHES ({len(matches)}): {', '.join(matches[:8])}")
            print(f"❌ MISSES ({len(misses)}): {', '.join(misses[:8])}")
            
            # Show some specific extractions
            print("\nKey extractions:")
            key_fields = ['first_name', 'last_name', 'birthDate', 'patient_phone_number', 'modality']
            for field in key_fields:
                if field in field_scores:
                    score = field_scores[field]
                    status = "✅" if score['match'] else "❌"
                    print(f"   {status} {field:20}: '{score['extracted'][:30]}' vs '{score['ground_truth'][:30]}'")
            
            overall_results.append({
                'filename': filename,
                'accuracy': file_accuracy,
                'total_fields': total_fields,
                'field_scores': field_scores,
                'matches': len(matches),
                'misses': len(misses)
            })
            
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
    
    # Calculate overall statistics
    print("\n" + "=" * 70)
    print("LLM-BASED EXTRACTOR RESULTS")
    print("=" * 70)
    
    if overall_results:
        avg_accuracy = sum(r['accuracy'] for r in overall_results) / len(overall_results)
        total_matches = sum(r['matches'] for r in overall_results)
        total_possible = sum(r['total_fields'] for r in overall_results)
        
        print(f"Average accuracy across test files: {avg_accuracy:.1f}%")
        print(f"Files tested: {len(overall_results)}")
        print(f"Total field matches: {total_matches}/{total_possible} ({(total_matches/total_possible)*100:.1f}%)")
        
        print(f"\nPer-file accuracy:")
        for result in overall_results:
            print(f"   {result['filename']:15} | {result['accuracy']:5.1f}% | {result['matches']:2d}/{result['total_fields']:2d} matches")
        
        # Field-level accuracy analysis
        print(f"\nField-level accuracy analysis:")
        print(f"{'Field Name':20} | {'Avg Accuracy':12} | {'Success Rate':12} | {'Tests':6}")
        print("-" * 60)
        
        excellent_fields = []
        good_fields = []
        poor_fields = []
        
        for field_name, scores in field_accuracy.items():
            avg_accuracy_field = (sum(scores) / len(scores)) * 100
            success_rate = (sum(1 for s in scores if s >= 0.8) / len(scores)) * 100
            test_count = len(scores)
            
            print(f"{field_name:20} | {avg_accuracy_field:10.1f}% | {success_rate:10.1f}% | {test_count:4d}")
            
            if avg_accuracy_field >= 80:
                excellent_fields.append((field_name, avg_accuracy_field))
            elif avg_accuracy_field >= 60:
                good_fields.append((field_name, avg_accuracy_field))
            else:
                poor_fields.append((field_name, avg_accuracy_field))
        
        # Performance categories
        print(f"\nPerformance Analysis:")
        print(f"   🟢 EXCELLENT (80%+ accuracy): {len(excellent_fields)} fields")
        for field, acc in excellent_fields:
            print(f"      {field}: {acc:.1f}%")
        
        print(f"   🟡 GOOD (60-80% accuracy): {len(good_fields)} fields")
        for field, acc in good_fields:
            print(f"      {field}: {acc:.1f}%")
        
        print(f"   🔴 POOR (<60% accuracy): {len(poor_fields)} fields")
        for field, acc in poor_fields[:10]:  # Show top 10
            print(f"      {field}: {acc:.1f}%")
        
        # Overall system assessment
        if avg_accuracy >= 80:
            assessment = "🟢 EXCELLENT - Production ready"
        elif avg_accuracy >= 70:
            assessment = "🟡 GOOD - Strong performance, ready for deployment"
        elif avg_accuracy >= 60:
            assessment = "🟠 FAIR - Functional but needs optimization"
        else:
            assessment = "🔴 POOR - Requires significant improvement"
        
        print(f"\nSystem Assessment: {assessment}")
        
        # Comparison with previous approaches
        print(f"\n📊 COMPARISON WITH PREVIOUS APPROACHES:")
        print(f"   Pattern-based system:     28.9% average accuracy")
        print(f"   Fixed rule-based system:  40.4% average accuracy")
        print(f"   Trained pattern system:   29.7% average accuracy")
        print(f"   LLM-based system:         {avg_accuracy:.1f}% average accuracy")
        
        if avg_accuracy > 40.4:
            improvement = avg_accuracy - 40.4
            print(f"   🚀 LLM improvement: +{improvement:.1f} percentage points over best previous system")
        else:
            decline = 40.4 - avg_accuracy
            print(f"   📉 LLM performance: -{decline:.1f} percentage points vs best previous system")
        
        # LLM effectiveness
        llm_effectiveness = (avg_accuracy - 28.9) / (100 - 28.9) * 100
        print(f"   📈 LLM effectiveness: {llm_effectiveness:.1f}% of maximum possible improvement")
        
        # Key advantages of LLM approach
        print(f"\n🎯 LLM APPROACH ADVANTAGES:")
        print(f"   ✅ Reads entire document context")
        print(f"   ✅ Understands medical terminology")
        print(f"   ✅ No hardcoded patterns")
        print(f"   ✅ Adapts to any document format")
        print(f"   ✅ Intelligent field disambiguation")
        print(f"   ✅ Consistent formatting output")
        
    return overall_results

if __name__ == "__main__":
    test_llm_extractor()
