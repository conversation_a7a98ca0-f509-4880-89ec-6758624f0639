import sys
import os
import boto3
import time
import json
import re
from typing import Dict, List, Any

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
from ground_truth_data import GROUND_TRUTH_DATA

class LLMBasedExtractor:
    def __init__(self):
        self.textract = boto3.client('textract', region_name='us-east-1')
        self.bedrock = boto3.client('bedrock-runtime', region_name='us-east-1')
        
    def extract_fields_from_pdf(self, bucket: str, key: str) -> Dict[str, str]:
        """Complete pipeline: PDF -> Textract -> LLM -> Formatted Fields"""
        
        # Step 1: Extract raw text from PDF using Textract
        raw_text = self._extract_raw_text_with_textract(bucket, key)
        if not raw_text:
            return {}
        
        # Step 2: Use LLM to intelligently extract fields
        extracted_fields = self._extract_fields_with_llm(raw_text)
        
        # Step 3: Format fields according to your specifications
        formatted_fields = self._format_extracted_fields(extracted_fields)
        
        return formatted_fields
    
    def _extract_raw_text_with_textract(self, bucket: str, key: str) -> str:
        """Extract complete raw text from PDF using Textract"""
        
        try:
            print(f"Extracting text from s3://{bucket}/{key}")
            
            # Try synchronous first (faster for smaller documents)
            try:
                response = self.textract.detect_document_text(
                    Document={
                        'S3Object': {
                            'Bucket': bucket,
                            'Name': key
                        }
                    }
                )
                
                raw_text = ""
                for block in response['Blocks']:
                    if block['BlockType'] == 'LINE':
                        raw_text += block['Text'] + "\n"
                
                print(f"Synchronous extraction: {len(raw_text)} characters")
                return raw_text
                
            except Exception as sync_error:
                print(f"Synchronous failed, trying asynchronous: {str(sync_error)}")
                
                # Use asynchronous for larger/complex documents
                response = self.textract.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket,
                            'Name': key
                        }
                    }
                )
                
                job_id = response['JobId']
                print(f"Started async job: {job_id}")
                
                # Wait for completion
                max_wait = 300
                wait_time = 0
                
                while wait_time < max_wait:
                    response = self.textract.get_document_text_detection(JobId=job_id)
                    status = response['JobStatus']
                    
                    if status == 'SUCCEEDED':
                        break
                    elif status == 'FAILED':
                        raise Exception(f"Textract job failed: {response.get('StatusMessage', 'Unknown error')}")
                    
                    time.sleep(5)
                    wait_time += 5
                
                if wait_time >= max_wait:
                    raise Exception("Textract job timeout")
                
                # Collect all text from paginated results
                raw_text = ""
                next_token = None
                
                while True:
                    if next_token:
                        response = self.textract.get_document_text_detection(
                            JobId=job_id,
                            NextToken=next_token
                        )
                    else:
                        response = self.textract.get_document_text_detection(JobId=job_id)
                    
                    for block in response['Blocks']:
                        if block['BlockType'] == 'LINE':
                            raw_text += block['Text'] + "\n"
                    
                    next_token = response.get('NextToken')
                    if not next_token:
                        break
                
                print(f"Asynchronous extraction: {len(raw_text)} characters")
                return raw_text
        
        except Exception as e:
            print(f"Error extracting text: {str(e)}")
            return ""
    
    def _extract_fields_with_llm(self, raw_text: str) -> Dict[str, str]:
        """Use LLM to intelligently extract medical fields from raw text"""
        
        # Create a comprehensive prompt for medical field extraction
        prompt = self._create_extraction_prompt(raw_text)
        
        try:
            print("Sending text to LLM for field extraction...")
            
            response = self.bedrock.invoke_model(
                modelId='anthropic.claude-3-5-sonnet-20240620-v1:0',  # Working Claude 3.5 Sonnet
                body=json.dumps({
                    "anthropic_version": "bedrock-2023-05-31",
                    "max_tokens": 4000,
                    "temperature": 0.1,  # Low temperature for consistent extraction
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                })
            )
            
            response_body = json.loads(response['body'].read())
            llm_response = response_body['content'][0]['text']
            
            print("LLM extraction completed")
            
            # Parse the LLM response to extract fields
            extracted_fields = self._parse_llm_response(llm_response)
            
            return extracted_fields
            
        except Exception as e:
            print(f"Error with LLM extraction: {str(e)}")
            return {}
    
    def _create_extraction_prompt(self, raw_text: str) -> str:
        """Create a detailed prompt for LLM field extraction"""
        
        prompt = f"""You are an expert medical document processor. I will provide you with the complete raw text extracted from a medical PDF document. Your task is to carefully read through ALL the text and extract the specific medical fields listed below.

IMPORTANT INSTRUCTIONS:
1. Read the ENTIRE document text carefully
2. Extract the EXACT values as they appear in the document
3. Do NOT make up or guess any information
4. If a field is not found, return an empty string ""
5. Return ONLY a valid JSON object with the field names as keys

FIELDS TO EXTRACT:
- patient_phone_number: The patient's phone number (not doctor's)
- doctor_phone_number: The ordering/referring physician's phone number (not patient's)
- doctor_Fax: Look specifically for fax numbers associated with the referring/ordering physician or their clinic. Search for text patterns like "Fax:", "FAX:", "F:", or numbers near the doctor's contact information. This should be the fax number of the facility SENDING the order, not receiving it. Format as XXX-XXX-XXXX.
- first_name: Patient's first name only
- last_name: Patient's last name only
- fullname: Patient's complete name
- birthDate: Patient's date of birth
- gender: Patient's gender (Male/Female)
- doctor_X: Ordering/referring physician's name
- doctor_NPI: Physician's NPI number (10 digits)
- doctor_address: Find the street address of the referring physician's clinic or medical office that is ORDERING/SENDING this imaging request. Look for the address that appears with the doctor's name, phone number, or NPI in the header/letterhead section. This is usually the first address that appears in the document and is associated with the ordering provider's contact information. Ignore any "Send to:" addresses, imaging center addresses, or patient addresses that appear later in the document.
- doctor_city: Physician's facility city
- doctor_state: Physician's facility state
- icd_codes: ICD diagnostic codes
- modality: Imaging modality (CT, MRI, XR, US, MM, etc.)
- exam: Complete examination description
- bodyPart: Anatomical region being examined - IMPORTANT: Include directional descriptors like "Right" or "Left" if they appear next to the body part (e.g. if you find "Leg", check if the full context is "Left Leg" or "Right Leg")
- insurance_number: Patient's insurance ID number
- doctor_exam_code: Search for 5-digit alphanumeric codes (like 73221, 70553, 72110) that appear near the medical procedure/exam description, diagnosis section, or billing information. These are typically CPT codes and may contain both numbers and letters. Look in sections mentioning "procedure", "exam", "study", "CPT", or near the medical order details. They are usually 5 characters long but can occasionally be 4-6 characters.

COMPLETE MEDICAL DOCUMENT TEXT:
{raw_text}

Extract the fields and return ONLY a JSON object in this exact format:
{{
    "patient_phone_number": "",
    "doctor_phone_number": "",
    "doctor_Fax": "",
    "first_name": "",
    "last_name": "",
    "fullname": "",
    "birthDate": "",
    "gender": "",
    "doctor_X": "",
    "doctor_NPI": "",
    "doctor_address": "",
    "doctor_city": "",
    "doctor_state": "",
    "icd_codes": "",
    "modality": "",
    "exam": "",
    "bodyPart": "",
    "insurance_number": "",
    "doctor_exam_code": ""
}}"""
        
        return prompt
    
    def _parse_llm_response(self, llm_response: str) -> Dict[str, str]:
        """Parse the LLM response to extract the JSON fields"""
        
        try:
            # Find JSON in the response
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = llm_response[json_start:json_end]
                extracted_fields = json.loads(json_str)
                
                print(f"Successfully parsed {len(extracted_fields)} fields from LLM response")
                return extracted_fields
            else:
                print("No JSON found in LLM response")
                return {}
                
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON from LLM response: {str(e)}")
            print(f"LLM Response: {llm_response[:500]}...")
            return {}
    
    def _format_extracted_fields(self, extracted_fields: Dict[str, str]) -> Dict[str, str]:
        """Format the extracted fields according to your specifications"""
        
        formatted_fields = {}
        
        for field_name, value in extracted_fields.items():
            if not value or value.strip() == "":
                formatted_fields[field_name] = ""
                continue
            
            value_str = str(value).strip()
            
            try:
                if field_name == 'birthDate':
                    formatted_fields[field_name] = self._format_birth_date(value_str)
                elif field_name in ['patient_phone_number', 'doctor_phone_number', 'doctor_fax']:
                    formatted_fields[field_name] = self._format_phone_number(value_str)
                elif field_name == 'fullname':
                    formatted_fields[field_name] = self._format_full_name(value_str)
                elif field_name == 'gender':
                    formatted_fields[field_name] = self._format_gender(value_str)
                elif field_name == 'doctor_state':
                    formatted_fields[field_name] = self._format_state(value_str)
                elif field_name == 'modality':
                    formatted_fields[field_name] = self._format_modality(value_str)
                elif field_name == 'bodyPart':
                    formatted_fields[field_name] = self._format_body_part(value_str)
                else:
                    formatted_fields[field_name] = value_str
                    
            except Exception as e:
                print(f"Error formatting {field_name}: {str(e)}")
                formatted_fields[field_name] = value_str
        
        return formatted_fields
    
    def _format_birth_date(self, date_str: str) -> str:
        """Format birth date to DD/MM/YYYY"""
        date_patterns = [
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, date_str)
            if match:
                parts = match.groups()
                if len(parts[2]) == 4:
                    month, day, year = parts  # Assume MM/DD/YYYY input
                else:
                    month, day, year = parts
                    year = f"20{year}" if int(year) < 50 else f"19{year}"
                
                # Convert to DD/MM/YYYY
                return f"{day.zfill(2)}/{month.zfill(2)}/{year}"
        
        return date_str
    
    def _format_phone_number(self, phone_str: str) -> str:
        """Format phone number to XXX-XXX-XXXX"""
        digits = re.sub(r'[^\d]', '', phone_str)
        if len(digits) == 10:
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits.startswith('1'):
            digits = digits[1:]
            return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        return phone_str
    
    def _format_full_name(self, name_str: str) -> str:
        """Format full name to 'Last, First Middle'"""
        if ',' in name_str:
            return name_str  # Already in correct format
        
        parts = name_str.split()
        if len(parts) >= 2:
            first = parts[0]
            last = parts[-1]
            middle = ' '.join(parts[1:-1]) if len(parts) > 2 else ''
            
            if middle:
                return f"{last}, {first} {middle}"
            else:
                return f"{last}, {first}"
        
        return name_str
    
    def _format_gender(self, gender_str: str) -> str:
        """Format gender to Male/Female"""
        gender_lower = gender_str.lower()
        if gender_lower in ['m', 'male']:
            return 'Male'
        elif gender_lower in ['f', 'female']:
            return 'Female'
        return gender_str
    
    def _format_state(self, state_str: str) -> str:
        """Format state to 2-letter code"""
        if len(state_str) == 2:
            return state_str.upper()
        
        state_codes = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
            'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
            'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
            'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
            'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
            'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
            'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
            'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
        }
        
        return state_codes.get(state_str.lower(), state_str)
    
    def _format_modality(self, modality_str: str) -> str:
        """Format modality to standard codes"""
        modality_lower = modality_str.lower()
        
        if 'ct' in modality_lower or 'computed tomography' in modality_lower:
            return 'CT'
        elif 'mri' in modality_lower or 'magnetic resonance' in modality_lower:
            return 'MRI'
        elif 'xray' in modality_lower or 'x-ray' in modality_lower or 'radiograph' in modality_lower:
            return 'XR'
        elif 'ultrasound' in modality_lower or 'sonography' in modality_lower:
            return 'US'
        elif 'mammography' in modality_lower or 'mammogram' in modality_lower:
            return 'MM'
        
        return modality_str.upper()
    
    def _format_body_part(self, body_part_str: str) -> str:
        """Format body part with proper directions"""
        body_part_lower = body_part_str.lower()
        
        # Standardize directions
        if 'right' in body_part_lower or ' r ' in body_part_lower or body_part_lower.startswith('r '):
            body_part_str = re.sub(r'\b(r|rt|right)\b', 'Right', body_part_str, flags=re.IGNORECASE)
        elif 'left' in body_part_lower or ' l ' in body_part_lower or body_part_lower.startswith('l '):
            body_part_str = re.sub(r'\b(l|lt|left)\b', 'Left', body_part_str, flags=re.IGNORECASE)
        
        return body_part_str.title()
