import sys
import os
import boto3
import time
from difflib import SequenceMatcher

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ground_truth_data import GROUND_TRUTH_DATA
from lambda_function.intelligent_lambda import IntelligentExtractor

def calculate_similarity(extracted, ground_truth):
    """Calculate similarity between extracted and ground truth values"""
    if not extracted and not ground_truth:
        return 1.0
    if not extracted or not ground_truth:
        return 0.0
    
    # Normalize strings for comparison
    extracted_clean = str(extracted).strip().lower()
    ground_truth_clean = str(ground_truth).strip().lower()
    
    # Special handling for phone numbers - consider same digits as match
    if any(field in ['phone', 'fax'] for field in ['phone', 'fax']):
        extracted_digits = ''.join(filter(str.isdigit, extracted_clean))
        truth_digits = ''.join(filter(str.isdigit, ground_truth_clean))
        if extracted_digits and truth_digits:
            return 1.0 if extracted_digits == truth_digits else 0.0
    
    # Use sequence matcher for similarity
    similarity = SequenceMatcher(None, extracted_clean, ground_truth_clean).ratio()
    
    # Consider 80%+ similarity as a match for lenient evaluation
    return 1.0 if similarity >= 0.8 else similarity

def test_accuracy_with_ground_truth():
    print("ACCURACY TEST WITH GROUND TRUTH DATA")
    print("=" * 70)
    
    # Files we tested in our previous run
    test_files = [
        "531087480.pdf",
        "531082057.pdf", 
        "531107444.pdf",
        "531146293.pdf",
        "531191234.pdf"
    ]
    
    textract = boto3.client('textract', region_name='us-east-1')
    extractor = IntelligentExtractor()
    
    overall_results = []
    field_accuracy = {}
    
    for i, filename in enumerate(test_files, 1):
        print(f"\nTEST {i}/5: {filename}")
        print("-" * 50)
        
        if filename not in GROUND_TRUTH_DATA:
            print(f"No ground truth data for {filename}")
            continue
        
        ground_truth = GROUND_TRUTH_DATA[filename]
        
        try:
            # Extract text and fields
            file_key = f"Imaging Orders OCR Data/{filename}"
            extracted_text = extract_text_with_textract(textract, "data-automation-dev-bucket", file_key)
            
            if not extracted_text:
                print("Failed to extract text")
                continue
            
            extracted_fields = extractor.extract_all_fields(extracted_text)
            
            # Calculate accuracy for each field
            field_scores = {}
            total_score = 0
            total_fields = 0
            
            for field_name in ground_truth.keys():
                if field_name in extracted_fields:
                    extracted_value = extracted_fields[field_name]
                    truth_value = ground_truth[field_name]
                    
                    similarity = calculate_similarity(extracted_value, truth_value)
                    field_scores[field_name] = {
                        'extracted': extracted_value,
                        'ground_truth': truth_value,
                        'similarity': similarity,
                        'match': similarity >= 0.8
                    }
                    
                    total_score += similarity
                    total_fields += 1
                    
                    # Track field-level accuracy
                    if field_name not in field_accuracy:
                        field_accuracy[field_name] = []
                    field_accuracy[field_name].append(similarity)
                else:
                    field_scores[field_name] = {
                        'extracted': '',
                        'ground_truth': ground_truth[field_name],
                        'similarity': 0.0,
                        'match': False
                    }
                    total_fields += 1
                    
                    if field_name not in field_accuracy:
                        field_accuracy[field_name] = []
                    field_accuracy[field_name].append(0.0)
            
            file_accuracy = (total_score / total_fields) * 100 if total_fields > 0 else 0
            
            print(f"Overall accuracy: {file_accuracy:.1f}%")
            print(f"Fields analyzed: {total_fields}")
            
            # Show field-by-field comparison
            print("\nField-by-field comparison:")
            for field_name, scores in field_scores.items():
                status = "MATCH" if scores['match'] else "MISS"
                similarity_pct = scores['similarity'] * 100
                
                print(f"   {status:5} {field_name:20} | {similarity_pct:5.1f}% | '{scores['extracted'][:30]}{'...' if len(str(scores['extracted'])) > 30 else ''}' vs '{scores['ground_truth'][:30]}{'...' if len(str(scores['ground_truth'])) > 30 else ''}'")
            
            overall_results.append({
                'filename': filename,
                'accuracy': file_accuracy,
                'total_fields': total_fields,
                'field_scores': field_scores
            })
            
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
    
    # Calculate overall statistics
    print("\n" + "=" * 70)
    print("OVERALL ACCURACY RESULTS")
    print("=" * 70)
    
    if overall_results:
        avg_accuracy = sum(r['accuracy'] for r in overall_results) / len(overall_results)
        print(f"Average accuracy across all files: {avg_accuracy:.1f}%")
        print(f"Files tested: {len(overall_results)}")
        
        print(f"\nPer-file accuracy:")
        for result in overall_results:
            print(f"   {result['filename']:15} | {result['accuracy']:5.1f}% | {result['total_fields']} fields")
        
        # Field-level accuracy analysis
        print(f"\nField-level accuracy analysis:")
        print(f"{'Field Name':20} | {'Avg Accuracy':12} | {'Success Rate':12} | {'Tests':6}")
        print("-" * 60)
        
        for field_name, scores in field_accuracy.items():
            avg_accuracy = (sum(scores) / len(scores)) * 100
            success_rate = (sum(1 for s in scores if s >= 0.8) / len(scores)) * 100
            test_count = len(scores)
            
            print(f"{field_name:20} | {avg_accuracy:10.1f}% | {success_rate:10.1f}% | {test_count:4d}")
        
        # Performance categories
        print(f"\nPerformance Analysis:")
        excellent_fields = [f for f, scores in field_accuracy.items() if (sum(scores)/len(scores)) >= 0.8]
        good_fields = [f for f, scores in field_accuracy.items() if 0.6 <= (sum(scores)/len(scores)) < 0.8]
        needs_work = [f for f, scores in field_accuracy.items() if (sum(scores)/len(scores)) < 0.6]
        
        print(f"   EXCELLENT (80%+ accuracy): {len(excellent_fields)} fields")
        for field in excellent_fields[:5]:  # Show top 5
            avg = (sum(field_accuracy[field])/len(field_accuracy[field])) * 100
            print(f"      {field}: {avg:.1f}%")
        
        print(f"   GOOD (60-80% accuracy): {len(good_fields)} fields")
        for field in good_fields[:5]:  # Show top 5
            avg = (sum(field_accuracy[field])/len(field_accuracy[field])) * 100
            print(f"      {field}: {avg:.1f}%")
        
        print(f"   NEEDS IMPROVEMENT (<60% accuracy): {len(needs_work)} fields")
        for field in needs_work[:5]:  # Show top 5
            avg = (sum(field_accuracy[field])/len(field_accuracy[field])) * 100
            print(f"      {field}: {avg:.1f}%")
        
        # Overall system assessment
        if avg_accuracy >= 80:
            assessment = "EXCELLENT - Production ready"
        elif avg_accuracy >= 70:
            assessment = "GOOD - Strong performance with room for improvement"
        elif avg_accuracy >= 60:
            assessment = "FAIR - Functional but needs optimization"
        else:
            assessment = "NEEDS WORK - Requires significant improvement"
        
        print(f"\nSystem Assessment: {assessment}")
        
    return overall_results

def extract_text_with_textract(textract, bucket, key):
    """Extract text using Textract (reused from previous test)"""
    try:
        try:
            response = textract.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            extracted_text = ""
            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    extracted_text += block['Text'] + "\n"
            
            return extracted_text
            
        except Exception:
            response = textract.start_document_text_detection(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            job_id = response['JobId']
            
            max_wait = 120
            wait_time = 0
            
            while wait_time < max_wait:
                response = textract.get_document_text_detection(JobId=job_id)
                status = response['JobStatus']
                
                if status == 'SUCCEEDED':
                    break
                elif status == 'FAILED':
                    raise Exception(f"Textract job failed")
                
                time.sleep(3)
                wait_time += 3
            
            if wait_time >= max_wait:
                raise Exception("Textract timeout")
            
            extracted_text = ""
            next_token = None
            
            while True:
                if next_token:
                    response = textract.get_document_text_detection(
                        JobId=job_id,
                        NextToken=next_token
                    )
                else:
                    response = textract.get_document_text_detection(JobId=job_id)
                
                for block in response['Blocks']:
                    if block['BlockType'] == 'LINE':
                        extracted_text += block['Text'] + "\n"
                
                next_token = response.get('NextToken')
                if not next_token:
                    break
            
            return extracted_text
            
    except Exception as e:
        print(f"Textract error: {str(e)}")
        return None

if __name__ == "__main__":
    test_accuracy_with_ground_truth()
