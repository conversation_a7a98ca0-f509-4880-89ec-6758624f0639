import boto3
import json
import time
import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from lambda_function.intelligent_lambda import IntelligentExtractor

def test_multiple_medical_files():
    print("INTELLIGENT EXTRACTION TEST ON REAL MEDICAL FILES")
    print("=" * 70)
    
    bucket = "data-automation-dev-bucket"
    test_files = [
        "Imaging Orders OCR Data/531087480.pdf",
        "Imaging Orders OCR Data/531082057.pdf", 
        "Imaging Orders OCR Data/531107444.pdf",
        "Imaging Orders OCR Data/531146293.pdf",
        "Imaging Orders OCR Data/531191234.pdf"
    ]
    
    textract = boto3.client('textract', region_name='us-east-1')
    extractor = IntelligentExtractor()
    
    overall_results = []
    
    for i, file_key in enumerate(test_files, 1):
        print(f"\nTEST {i}/5: {file_key}")
        print("-" * 50)
        
        try:
            extracted_text = extract_text_with_textract(textract, bucket, file_key)
            
            if extracted_text:
                print(f"Text extracted: {len(extracted_text):,} characters")
                
                fields = extractor.extract_all_fields(extracted_text)
                
                successful_extractions = sum(1 for v in fields.values() if v)
                total_fields = len(fields)
                success_rate = (successful_extractions / total_fields) * 100
                
                print(f"Fields extracted: {successful_extractions}/{total_fields} ({success_rate:.1f}%)")
                
                print("\nExtracted fields:")
                for field, value in fields.items():
                    status = "FOUND" if value else "EMPTY"
                    if value:
                        print(f"   {status:5} {field:20}: '{value[:50]}{'...' if len(str(value)) > 50 else ''}'")
                
                overall_results.append({
                    'file': file_key,
                    'success_rate': success_rate,
                    'fields_found': successful_extractions,
                    'total_fields': total_fields,
                    'text_length': len(extracted_text)
                })
                
                print(f"\nSample extracted text (first 300 chars):")
                print("-" * 30)
                print(extracted_text[:300])
                print("-" * 30)
                
            else:
                print("Failed to extract text")
                overall_results.append({
                    'file': file_key,
                    'success_rate': 0,
                    'fields_found': 0,
                    'total_fields': 19,
                    'text_length': 0
                })
                
        except Exception as e:
            print(f"Error processing {file_key}: {str(e)}")
            overall_results.append({
                'file': file_key,
                'success_rate': 0,
                'fields_found': 0,
                'total_fields': 19,
                'text_length': 0
            })
    
    print("\n" + "=" * 70)
    print("OVERALL TEST RESULTS")
    print("=" * 70)
    
    total_success_rate = sum(r['success_rate'] for r in overall_results) / len(overall_results)
    total_fields_found = sum(r['fields_found'] for r in overall_results)
    total_possible_fields = sum(r['total_fields'] for r in overall_results)
    
    print(f"Files tested: {len(test_files)}")
    print(f"Average success rate: {total_success_rate:.1f}%")
    print(f"Total fields found: {total_fields_found}/{total_possible_fields}")
    print(f"Overall extraction rate: {(total_fields_found/total_possible_fields)*100:.1f}%")
    
    print(f"\nPer-file results:")
    for result in overall_results:
        filename = result['file'].split('/')[-1]
        print(f"   {filename:15} | {result['success_rate']:5.1f}% | {result['fields_found']:2d}/{result['total_fields']:2d} fields | {result['text_length']:,} chars")
    
    print(f"\nSystem Performance:")
    if total_success_rate >= 70:
        print("   EXCELLENT - System handles diverse medical documents very well")
    elif total_success_rate >= 50:
        print("   GOOD - System shows strong adaptive capabilities")
    elif total_success_rate >= 30:
        print("   FAIR - System extracts key fields from most documents")
    else:
        print("   NEEDS IMPROVEMENT - Consider additional training data")
    
    return overall_results

def extract_text_with_textract(textract, bucket, key):
    try:
        print(f"Extracting text from {key}...")
        
        try:
            response = textract.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            extracted_text = ""
            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    extracted_text += block['Text'] + "\n"
            
            return extracted_text
            
        except Exception:
            response = textract.start_document_text_detection(
                DocumentLocation={
                    'S3Object': {
                        'Bucket': bucket,
                        'Name': key
                    }
                }
            )
            
            job_id = response['JobId']
            
            max_wait = 120
            wait_time = 0
            
            while wait_time < max_wait:
                response = textract.get_document_text_detection(JobId=job_id)
                status = response['JobStatus']
                
                if status == 'SUCCEEDED':
                    break
                elif status == 'FAILED':
                    raise Exception(f"Textract job failed")
                
                time.sleep(3)
                wait_time += 3
            
            if wait_time >= max_wait:
                raise Exception("Textract timeout")
            
            extracted_text = ""
            next_token = None
            
            while True:
                if next_token:
                    response = textract.get_document_text_detection(
                        JobId=job_id,
                        NextToken=next_token
                    )
                else:
                    response = textract.get_document_text_detection(JobId=job_id)
                
                for block in response['Blocks']:
                    if block['BlockType'] == 'LINE':
                        extracted_text += block['Text'] + "\n"
                
                next_token = response.get('NextToken')
                if not next_token:
                    break
            
            return extracted_text
            
    except Exception as e:
        print(f"Textract error: {str(e)}")
        return None

if __name__ == "__main__":
    test_multiple_medical_files()
