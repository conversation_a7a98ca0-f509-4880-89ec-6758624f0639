import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lambda_function.intelligent_lambda import IntelligentExtractor

def test_intelligent_extraction():
    print("INTELLIGENT MEDICAL FIELD EXTRACTION TEST")
    print("=" * 60)
    
    sample_texts = [
        {
            "name": "Sample Medical Document 1",
            "text": """
            MEDICAL IMAGING ORDER
            
            Patient Information:
            Name: <PERSON>
            DOB: 03/15/1985
            Gender: Female
            Phone: (*************
            
            Referring Physician:
            Dr. <PERSON>, MD
            NPI: **********
            Phone: (*************
            Fax: (*************
            Address: 123 Medical Center Drive, Suite 200
            City: Springfield, IL 62701
            
            Study Information:
            Exam: CT Scan Chest with Contrast
            Modality: CT
            Body Part: Chest
            ICD-10: J44.1, Z87.891
            CPT Code: 71260
            
            Insurance:
            Policy Number: ABC123456789
            """
        },
        {
            "name": "Sample Medical Document 2", 
            "text": """
            RADIOLOGY REQUEST FORM
            
            PT: MARTINEZ, CARLOS R
            Birth Date: 12/08/1972
            Sex: M
            Contact: ************
            
            ORDERING PROVIDER:
            <PERSON> MD
            Provider ID: **********
            Tel: ************
            Facsimile: ************
            Location: 456 Health Plaza Blvd
            St. Louis, MO 63110
            
            PROCEDURE REQUESTED:
            MRI Right Knee without contrast
            Anatomical Region: Right Knee
            Reason: M25.561
            Procedure Code: 73721
            
            MEMBER ID: XYZ987654321
            """
        },
        {
            "name": "Sample Medical Document 3",
            "text": """
            IMAGING AUTHORIZATION
            
            PATIENT DATA
            Last Name: Thompson
            First Name: Emily
            Date of Birth: 07/22/1990
            Gender: F
            Phone Number: ************
            
            PHYSICIAN INFORMATION
            Doctor: Robert Kim
            NPI Number: **********
            Office Phone: ************
            Fax Number: ************
            Practice Address: 789 Wellness Way
            Chicago, Illinois 60601
            
            IMAGING STUDY
            Type: Ultrasound Abdomen Complete
            Modality: US
            Area: Abdomen
            Diagnosis Code: R10.9
            Billing Code: 76700
            
            INSURANCE INFO
            Member Number: DEF456789012
            """
        }
    ]
    
    extractor = IntelligentExtractor()
    
    for i, sample in enumerate(sample_texts, 1):
        print(f"\nTEST {i}: {sample['name']}")
        print("-" * 40)
        
        results = extractor.extract_all_fields(sample['text'])
        
        successful_extractions = 0
        total_fields = len(results)
        
        for field, value in results.items():
            status = "FOUND" if value else "EMPTY"
            if value:
                successful_extractions += 1
            print(f"   {status:5} {field:20}: '{value}'")
        
        success_rate = (successful_extractions / total_fields) * 100
        print(f"\n   SUCCESS RATE: {successful_extractions}/{total_fields} ({success_rate:.1f}%)")
        print()
    
    print("=" * 60)
    print("INTELLIGENT EXTRACTION FEATURES:")
    print("- Context-aware field detection")
    print("- Adaptive pattern matching")
    print("- Medical terminology recognition")
    print("- Multi-format date handling")
    print("- Intelligent name parsing")
    print("- Anatomical region standardization")
    print("- Phone number normalization")
    print("- State code conversion")
    print("- No hardcoded patterns")
    print("- Works with any medical document format")

if __name__ == "__main__":
    test_intelligent_extraction()
