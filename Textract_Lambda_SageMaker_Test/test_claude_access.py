import boto3
import json

def test_claude_access():
    """Test Claude 3.5 Sonnet access and find the correct model ID"""
    
    print("TESTING CLAUDE 3.5 SONNET ACCESS")
    print("=" * 50)
    
    try:
        bedrock = boto3.client('bedrock-runtime', region_name='us-east-1')
        
        # Test different possible model IDs for Claude 3.5 Sonnet
        possible_model_ids = [
            'anthropic.claude-3-5-sonnet-20241022-v2:0',
            'anthropic.claude-3-5-sonnet-20240620-v1:0',
            'anthropic.claude-3-sonnet-20240229-v1:0',
            'anthropic.claude-3-5-sonnet-v2:0',
            'anthropic.claude-3-5-sonnet-v1:0'
        ]
        
        test_prompt = "Extract the patient name from this text: Patient: <PERSON>, DOB: 01/01/1990"
        
        for model_id in possible_model_ids:
            print(f"\nTesting model ID: {model_id}")
            
            try:
                response = bedrock.invoke_model(
                    modelId=model_id,
                    body=json.dumps({
                        "anthropic_version": "bedrock-2023-05-31",
                        "max_tokens": 100,
                        "temperature": 0.1,
                        "messages": [
                            {
                                "role": "user",
                                "content": test_prompt
                            }
                        ]
                    })
                )
                
                response_body = json.loads(response['body'].read())
                llm_response = response_body['content'][0]['text']
                
                print(f"✅ SUCCESS with {model_id}")
                print(f"Response: {llm_response[:100]}...")
                print(f"This is the correct model ID to use!")
                return model_id
                
            except Exception as e:
                print(f"❌ FAILED with {model_id}: {str(e)}")
                continue
        
        print("\n❌ None of the model IDs worked. Let's check what models are available...")
        
        # List available models
        bedrock_models = boto3.client('bedrock', region_name='us-east-1')
        response = bedrock_models.list_foundation_models()
        
        claude_models = [model for model in response['modelSummaries'] 
                        if 'claude' in model['modelId'].lower()]
        
        print(f"\nAvailable Claude models:")
        for model in claude_models:
            print(f"  - {model['modelId']}")
            print(f"    Status: {model.get('modelLifecycle', {}).get('status', 'Unknown')}")
        
        return None
        
    except Exception as e:
        print(f"Error testing Claude access: {str(e)}")
        return None

def test_simple_extraction():
    """Test simple medical field extraction once we find the working model"""
    
    working_model = test_claude_access()
    
    if not working_model:
        print("\n❌ Could not find working Claude model")
        return
    
    print(f"\n{'='*50}")
    print("TESTING MEDICAL FIELD EXTRACTION")
    print(f"Using model: {working_model}")
    print("=" * 50)
    
    # Simple medical text for testing
    test_text = """
    MEDICAL RECORD
    
    Patient Information:
    Name: Sarah Johnson
    DOB: 03/15/1985
    Gender: Female
    Phone: (*************
    
    Physician: Dr. Michael Chen
    NPI: **********
    Phone: (*************
    
    Exam: CT Scan Chest
    Modality: CT
    Body Part: Chest
    """
    
    prompt = f"""Extract these medical fields from the text and return as JSON:
- first_name
- last_name
- birthDate (format as DD/MM/YYYY)
- gender
- patient_phone_number (format as XXX-XXX-XXXX)
- doctor_X
- doctor_NPI
- doctor_phone_number (format as XXX-XXX-XXXX)
- modality
- exam
- bodyPart

Text: {test_text}

Return only JSON:"""
    
    try:
        bedrock = boto3.client('bedrock-runtime', region_name='us-east-1')
        
        response = bedrock.invoke_model(
            modelId=working_model,
            body=json.dumps({
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 1000,
                "temperature": 0.1,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            })
        )
        
        response_body = json.loads(response['body'].read())
        llm_response = response_body['content'][0]['text']
        
        print("✅ LLM Response:")
        print(llm_response)
        
        # Try to parse JSON
        try:
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = llm_response[json_start:json_end]
                extracted_fields = json.loads(json_str)
                
                print("\n✅ Successfully extracted fields:")
                for field, value in extracted_fields.items():
                    print(f"  {field}: '{value}'")
                
                print(f"\n🎉 SUCCESS! Claude 3.5 Sonnet is working perfectly!")
                print(f"Model ID to use: {working_model}")
                
            else:
                print("❌ Could not find JSON in response")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing error: {str(e)}")
            
    except Exception as e:
        print(f"❌ Error in extraction test: {str(e)}")

if __name__ == "__main__":
    test_simple_extraction()
