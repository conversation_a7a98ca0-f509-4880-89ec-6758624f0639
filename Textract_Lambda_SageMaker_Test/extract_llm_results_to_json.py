import sys
import os
import json
from typing import Dict

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ground_truth_data import GROUND_TRUTH_DATA
from llm_extractor import LLMBasedExtractor

def extract_all_llm_results_to_json():
    """Extract all 40 documents using LLM and save results in ground_truth format"""
    
    print("EXTRACTING ALL 40 DOCUMENTS WITH LLM")
    print("=" * 60)
    print("Creating JSON file with LLM extraction results")
    print("Format: Same as ground_truth_data.py for easy comparison")
    print("=" * 60)
    
    # Get all files from ground truth data
    all_files = list(GROUND_TRUTH_DATA.keys())
    print(f"Total files to process: {len(all_files)}")
    
    extractor = LLMBasedExtractor()
    llm_results = {}
    
    successful_extractions = 0
    failed_extractions = 0
    
    for i, filename in enumerate(all_files, 1):
        print(f"\nProcessing {i}/{len(all_files)}: {filename}")
        
        try:
            # Extract fields using LLM-based approach
            file_key = f"Imaging Orders OCR Data/{filename}"
            extracted_fields = extractor.extract_fields_from_pdf("data-automation-dev-bucket", file_key)
            
            if extracted_fields:
                # Ensure all expected fields are present (use empty string if missing)
                complete_fields = {}
                expected_fields = list(GROUND_TRUTH_DATA[filename].keys())
                
                for field in expected_fields:
                    complete_fields[field] = extracted_fields.get(field, "")
                
                llm_results[filename] = complete_fields
                successful_extractions += 1
                print(f"✅ Success - {len([v for v in complete_fields.values() if v])}/{len(complete_fields)} fields extracted")
                
            else:
                print("❌ Failed to extract fields")
                # Create empty result with all expected fields
                llm_results[filename] = {field: "" for field in GROUND_TRUTH_DATA[filename].keys()}
                failed_extractions += 1
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            # Create empty result with all expected fields
            llm_results[filename] = {field: "" for field in GROUND_TRUTH_DATA[filename].keys()}
            failed_extractions += 1
    
    # Save results to JSON file
    output_file = "llm_extraction_results.json"
    
    # Create the complete data structure
    output_data = {
        "metadata": {
            "extraction_method": "LLM-based (Claude 3.5 Sonnet)",
            "model_id": "anthropic.claude-3-5-sonnet-20240620-v1:0",
            "total_files": len(all_files),
            "successful_extractions": successful_extractions,
            "failed_extractions": failed_extractions,
            "success_rate": f"{(successful_extractions/len(all_files))*100:.1f}%",
            "extraction_date": "2024-12-28",
            "notes": "Results from comprehensive 40-document LLM extraction test"
        },
        "results": llm_results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n" + "=" * 60)
    print("EXTRACTION COMPLETE")
    print("=" * 60)
    print(f"✅ Results saved to: {output_file}")
    print(f"📊 Summary:")
    print(f"   Total files: {len(all_files)}")
    print(f"   Successful: {successful_extractions}")
    print(f"   Failed: {failed_extractions}")
    print(f"   Success rate: {(successful_extractions/len(all_files))*100:.1f}%")
    
    # Also create a Python file in the same format as ground_truth_data.py
    python_output_file = "llm_extraction_results.py"
    
    with open(python_output_file, 'w', encoding='utf-8') as f:
        f.write('# LLM Extraction Results\n')
        f.write('# Generated from Claude 3.5 Sonnet extraction on all 40 medical documents\n')
        f.write('# Format matches ground_truth_data.py for easy comparison\n\n')
        f.write('LLM_EXTRACTION_RESULTS = {\n')
        
        for filename, fields in llm_results.items():
            f.write(f'    "{filename}": {{\n')
            for field_name, value in fields.items():
                # Escape quotes in values
                escaped_value = str(value).replace('"', '\\"')
                f.write(f'        "{field_name}": "{escaped_value}",\n')
            f.write('    },\n')
        
        f.write('}\n')
    
    print(f"✅ Python format saved to: {python_output_file}")
    
    # Create a comparison summary
    create_comparison_summary(llm_results)
    
    return llm_results

def create_comparison_summary(llm_results: Dict):
    """Create a summary comparing LLM results to ground truth"""
    
    print(f"\n" + "=" * 60)
    print("CREATING COMPARISON SUMMARY")
    print("=" * 60)
    
    comparison_summary = {
        "field_accuracy": {},
        "file_accuracy": {},
        "overall_stats": {}
    }
    
    # Calculate field-level accuracy
    field_totals = {}
    field_matches = {}
    
    for filename in llm_results.keys():
        if filename in GROUND_TRUTH_DATA:
            ground_truth = GROUND_TRUTH_DATA[filename]
            llm_result = llm_results[filename]
            
            file_matches = 0
            file_total = 0
            
            for field_name in ground_truth.keys():
                if field_name not in field_totals:
                    field_totals[field_name] = 0
                    field_matches[field_name] = 0
                
                field_totals[field_name] += 1
                file_total += 1
                
                # Simple comparison (can be made more sophisticated)
                gt_value = str(ground_truth[field_name]).strip().lower()
                llm_value = str(llm_result.get(field_name, "")).strip().lower()
                
                if gt_value and llm_value:
                    # Consider it a match if 80% similar or exact match
                    from difflib import SequenceMatcher
                    similarity = SequenceMatcher(None, gt_value, llm_value).ratio()
                    if similarity >= 0.8:
                        field_matches[field_name] += 1
                        file_matches += 1
                elif not gt_value and not llm_value:
                    # Both empty - consider a match
                    field_matches[field_name] += 1
                    file_matches += 1
            
            file_accuracy = (file_matches / file_total) * 100 if file_total > 0 else 0
            comparison_summary["file_accuracy"][filename] = {
                "accuracy": file_accuracy,
                "matches": file_matches,
                "total": file_total
            }
    
    # Calculate field accuracy
    for field_name in field_totals.keys():
        accuracy = (field_matches[field_name] / field_totals[field_name]) * 100
        comparison_summary["field_accuracy"][field_name] = {
            "accuracy": accuracy,
            "matches": field_matches[field_name],
            "total": field_totals[field_name]
        }
    
    # Overall statistics
    total_matches = sum(field_matches.values())
    total_possible = sum(field_totals.values())
    overall_accuracy = (total_matches / total_possible) * 100 if total_possible > 0 else 0
    
    comparison_summary["overall_stats"] = {
        "overall_accuracy": overall_accuracy,
        "total_matches": total_matches,
        "total_possible": total_possible,
        "files_processed": len([f for f in comparison_summary["file_accuracy"].keys()])
    }
    
    # Save comparison summary
    with open("llm_vs_ground_truth_comparison.json", 'w', encoding='utf-8') as f:
        json.dump(comparison_summary, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Comparison summary saved to: llm_vs_ground_truth_comparison.json")
    print(f"📊 Overall accuracy: {overall_accuracy:.1f}%")
    print(f"📊 Total matches: {total_matches}/{total_possible}")
    
    return comparison_summary

if __name__ == "__main__":
    extract_all_llm_results_to_json()
