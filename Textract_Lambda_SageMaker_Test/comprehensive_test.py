import boto3
import json
import time
import re
from field_definitions import FIELD_DEFINITIONS
from formatting_functions import *

def test_comprehensive_extraction():
    bucket = "data-automation-dev-bucket"
    key = "Imaging Orders OCR Data/531087480.pdf"
    
    print("COMPREHENSIVE MEDICAL PDF PROCESSING TEST")
    print("=" * 60)
    print(f"Testing file: {key}")
    print()
    
    textract = boto3.client('textract', region_name='us-east-1')
    
    print("Step 1: Extracting text with Textract...")
    
    try:
        response = textract.start_document_text_detection(
            DocumentLocation={
                'S3Object': {
                    'Bucket': bucket,
                    'Name': key
                }
            }
        )
        
        job_id = response['JobId']
        print(f"Job ID: {job_id}")
        
        while True:
            response = textract.get_document_text_detection(JobId=job_id)
            status = response['JobStatus']
            
            if status == 'SUCCEEDED':
                break
            elif status == 'FAILED':
                print(f"Job failed: {response.get('StatusMessage')}")
                return
            
            time.sleep(3)
        
        extracted_text = ""
        next_token = None
        
        while True:
            if next_token:
                response = textract.get_document_text_detection(
                    JobId=job_id,
                    NextToken=next_token
                )
            else:
                response = textract.get_document_text_detection(JobId=job_id)
            
            for block in response['Blocks']:
                if block['BlockType'] == 'LINE':
                    extracted_text += block['Text'] + "\n"
            
            next_token = response.get('NextToken')
            if not next_token:
                break
        
        print(f"Text extraction successful: {len(extracted_text)} characters")
        
        print("\nStep 2: Extracting medical fields...")
        
        fields = {}
        
        for field_name, field_config in FIELD_DEFINITIONS.items():
            patterns = field_config.get('patterns', [])
            extracted_value = ""
            
            for pattern in patterns:
                try:
                    matches = re.findall(pattern, extracted_text, re.IGNORECASE | re.MULTILINE)
                    if matches:
                        if field_name == 'icd_codes':
                            extracted_value = ', '.join(matches[:3])
                        else:
                            extracted_value = matches[0] if isinstance(matches[0], str) else matches[0]
                        break
                except Exception:
                    continue
            
            fields[field_name] = extracted_value
        
        print("\nStep 3: Applying formatting rules...")
        
        formatted_fields = {}
        
        for field, value in fields.items():
            if not value:
                formatted_fields[field] = ""
                continue
                
            value_str = str(value).strip()
            
            try:
                if field == 'birthDate':
                    formatted_fields[field] = format_birth_date(value_str)
                elif field == 'bodyPart':
                    formatted_fields[field] = format_body_part(value_str)
                elif field == 'doctor_address':
                    formatted_fields[field] = format_doctor_address(value_str)
                elif field == 'doctor_city':
                    formatted_fields[field] = format_doctor_city(value_str)
                elif field == 'doctor_exam_codes':
                    formatted_fields[field] = format_exam_codes(value_str)
                elif field in ['doctor_fax', 'doctor_phone_number', 'patient_phone_number']:
                    formatted_fields[field] = format_phone_number(value_str)
                elif field == 'doctor_NPI':
                    formatted_fields[field] = format_npi(value_str)
                elif field == 'doctor_state':
                    formatted_fields[field] = format_state(value_str)
                elif field == 'doctor_X':
                    formatted_fields[field] = format_doctor_name(value_str)
                elif field == 'exam':
                    formatted_fields[field] = format_exam(value_str)
                elif field == 'first_name':
                    formatted_fields[field] = format_first_name(value_str)
                elif field == 'last_name':
                    formatted_fields[field] = format_last_name(value_str)
                elif field == 'fullname':
                    formatted_fields[field] = format_full_name(value_str)
                elif field == 'gender':
                    formatted_fields[field] = format_gender(value_str)
                elif field == 'icd_codes':
                    formatted_fields[field] = format_icd_codes(value_str)
                elif field == 'insurance_number':
                    formatted_fields[field] = format_insurance_number(value_str)
                elif field == 'modality':
                    formatted_fields[field] = format_modality(value_str)
                else:
                    formatted_fields[field] = value_str
                    
            except Exception as e:
                formatted_fields[field] = value_str
        
        print("\nStep 4: Results Analysis")
        print("-" * 40)
        
        successful_extractions = 0
        total_fields = len(formatted_fields)
        
        for field, value in formatted_fields.items():
            status = "FOUND" if value else "EMPTY"
            if value:
                successful_extractions += 1
            print(f"   {status:5} {field:20}: '{value}'")
        
        print(f"\nSUMMARY:")
        print(f"Text extracted: {len(extracted_text):,} characters")
        print(f"Fields extracted: {successful_extractions}/{total_fields}")
        print(f"Success rate: {successful_extractions/total_fields*100:.1f}%")
        
        print(f"\nSample of extracted text:")
        print("-" * 40)
        print(extracted_text[:2000])
        print("-" * 40)
        
        return formatted_fields, extracted_text
        
    except Exception as e:
        print(f"Error: {str(e)}")
        return None, None

if __name__ == "__main__":
    test_comprehensive_extraction()
