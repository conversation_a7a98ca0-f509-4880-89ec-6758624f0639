import sys
import os
import json
import time
import random
from difflib import SequenceMatcher

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ground_truth_data import GROUND_TRUTH_DATA
from llm_extractor import LLMBasedExtractor

def calculate_similarity(extracted, ground_truth):
    """Calculate similarity between extracted and ground truth values with lenient evaluation"""
    if not extracted and not ground_truth:
        return 1.0
    if not extracted or not ground_truth:
        return 0.0
    
    extracted_clean = str(extracted).strip().lower()
    ground_truth_clean = str(ground_truth).strip().lower()
    
    # Special handling for phone numbers - consider same digits as match
    if any(char.isdigit() for char in str(extracted)) and any(char.isdigit() for char in str(ground_truth)):
        extracted_digits = ''.join(filter(str.isdigit, extracted_clean))
        truth_digits = ''.join(filter(str.isdigit, ground_truth_clean))
        if extracted_digits and truth_digits and len(extracted_digits) >= 10 and len(truth_digits) >= 10:
            return 1.0 if extracted_digits == truth_digits else 0.0
    
    # Special handling for body parts - lenient matching
    if any(word in ground_truth_clean for word in ['shoulder', 'spine', 'brain', 'chest', 'abdomen', 'knee', 'ankle']):
        if any(word in extracted_clean for word in ground_truth_clean.split()):
            return 1.0
    
    # Use sequence matcher for similarity
    similarity = SequenceMatcher(None, extracted_clean, ground_truth_clean).ratio()
    
    # Consider 80%+ similarity as a match for lenient evaluation
    return 1.0 if similarity >= 0.8 else similarity

def test_5_random_files():
    """Test the optimized LLM extractor on 5 random files"""
    
    print("TESTING 5 RANDOM FILES WITH OPTIMIZED LLM SYSTEM")
    print("=" * 70)
    print("Model: Claude 3.5 Sonnet with AI-optimized instructions")
    print("Testing: Perfect formatting + FROM/TO distinction + Field consistency")
    print("=" * 70)
    
    # Get all files and select 5 random ones
    all_files = list(GROUND_TRUTH_DATA.keys())
    random.seed(42)  # For reproducible results
    test_files = random.sample(all_files, 5)
    
    print(f"Selected files: {', '.join(test_files)}")
    print()
    
    extractor = LLMBasedExtractor()
    
    results = []
    total_processing_time = 0
    
    for i, filename in enumerate(test_files, 1):
        print(f"TEST {i}/5: {filename}")
        print("-" * 50)
        
        ground_truth = GROUND_TRUTH_DATA[filename]
        start_time = time.time()
        
        try:
            # Extract fields using optimized LLM
            file_key = f"Imaging Orders OCR Data/{filename}"
            extracted_fields = extractor.extract_fields_from_pdf("data-automation-dev-bucket", file_key)
            
            processing_time = time.time() - start_time
            total_processing_time += processing_time
            
            if not extracted_fields:
                print("❌ Failed to extract fields")
                results.append({
                    'filename': filename,
                    'success': False,
                    'accuracy': 0,
                    'processing_time': processing_time
                })
                continue
            
            # Calculate accuracy for each field
            field_scores = {}
            total_score = 0
            total_fields = 0
            
            for field_name in ground_truth.keys():
                extracted_value = extracted_fields.get(field_name, "")
                truth_value = ground_truth[field_name]
                
                similarity = calculate_similarity(extracted_value, truth_value)
                field_scores[field_name] = {
                    'extracted': extracted_value,
                    'ground_truth': truth_value,
                    'similarity': similarity,
                    'match': similarity >= 0.8
                }
                
                total_score += similarity
                total_fields += 1
            
            file_accuracy = (total_score / total_fields) * 100 if total_fields > 0 else 0
            
            # Count matches and misses
            matches = [f for f, s in field_scores.items() if s['match']]
            misses = [f for f, s in field_scores.items() if not s['match']]
            
            print(f"✅ Accuracy: {file_accuracy:.1f}% | Matches: {len(matches)}/{total_fields} | Time: {processing_time:.1f}s")
            
            # Show key field results
            key_fields = ['first_name', 'last_name', 'doctor_X', 'doctor_address', 'doctor_phone_number', 'bodyPart', 'modality']
            key_results = []
            for field in key_fields:
                if field in field_scores:
                    score = field_scores[field]
                    status = "✅" if score['match'] else "❌"
                    key_results.append(f"{status}{field}")
            
            if key_results:
                print(f"Key fields: {' '.join(key_results[:4])}")
            
            # Show any misses
            if misses:
                print(f"❌ Misses: {', '.join(misses[:3])}")
            
            results.append({
                'filename': filename,
                'success': True,
                'accuracy': file_accuracy,
                'total_fields': total_fields,
                'field_scores': field_scores,
                'matches': len(matches),
                'misses': len(misses),
                'processing_time': processing_time
            })
            
        except Exception as e:
            processing_time = time.time() - start_time
            total_processing_time += processing_time
            print(f"❌ Error processing {filename}: {str(e)}")
            results.append({
                'filename': filename,
                'success': False,
                'accuracy': 0,
                'processing_time': processing_time,
                'error': str(e)
            })
        
        print()
    
    # Calculate overall statistics
    print("=" * 70)
    print("RANDOM SAMPLE RESULTS SUMMARY")
    print("=" * 70)
    
    successful_results = [r for r in results if r['success']]
    
    if successful_results:
        avg_accuracy = sum(r['accuracy'] for r in successful_results) / len(successful_results)
        total_matches = sum(r['matches'] for r in successful_results)
        total_possible = sum(r['total_fields'] for r in successful_results)
        avg_processing_time = total_processing_time / len(test_files)
        
        print(f"📊 PERFORMANCE METRICS:")
        print(f"   Files successfully processed: {len(successful_results)}/5")
        print(f"   Average accuracy: {avg_accuracy:.1f}%")
        print(f"   Total field matches: {total_matches}/{total_possible} ({(total_matches/total_possible)*100:.1f}%)")
        print(f"   Average processing time: {avg_processing_time:.1f} seconds per document")
        print(f"   Total processing time: {total_processing_time:.1f} seconds")
        
        # Performance distribution
        excellent_files = [r for r in successful_results if r['accuracy'] >= 90]
        good_files = [r for r in successful_results if 80 <= r['accuracy'] < 90]
        fair_files = [r for r in successful_results if 70 <= r['accuracy'] < 80]
        poor_files = [r for r in successful_results if r['accuracy'] < 70]
        
        print(f"\n📈 PERFORMANCE DISTRIBUTION:")
        print(f"   Excellent (90%+): {len(excellent_files)} files")
        print(f"   Good (80-90%): {len(good_files)} files")
        print(f"   Fair (70-80%): {len(fair_files)} files")
        print(f"   Poor (<70%): {len(poor_files)} files")
        
        # Individual file results
        print(f"\n📋 INDIVIDUAL FILE RESULTS:")
        for result in successful_results:
            status = "🟢" if result['accuracy'] >= 90 else "🟡" if result['accuracy'] >= 80 else "🟠" if result['accuracy'] >= 70 else "🔴"
            print(f"   {status} {result['filename']:15} | {result['accuracy']:5.1f}% | {result['matches']:2d}/{result['total_fields']:2d} matches")
        
        # System assessment
        if avg_accuracy >= 90:
            assessment = "🟢 EXCELLENT - Production ready with outstanding performance"
        elif avg_accuracy >= 80:
            assessment = "🟡 GOOD - Production ready with solid performance"
        elif avg_accuracy >= 70:
            assessment = "🟠 FAIR - Functional but may need optimization"
        else:
            assessment = "🔴 POOR - Requires improvement"
        
        print(f"\n🎯 SYSTEM ASSESSMENT: {assessment}")
        
        # Save detailed results
        output_data = {
            "test_type": "5_random_files",
            "test_date": "2024-12-28",
            "model": "Claude 3.5 Sonnet (optimized)",
            "files_tested": test_files,
            "summary": {
                "avg_accuracy": avg_accuracy,
                "total_matches": total_matches,
                "total_possible": total_possible,
                "success_rate": len(successful_results) / len(test_files),
                "avg_processing_time": avg_processing_time
            },
            "detailed_results": results
        }
        
        with open("5_random_files_test_results.json", 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Detailed results saved to: 5_random_files_test_results.json")
        
    else:
        print("❌ No successful extractions to analyze")
    
    return results

if __name__ == "__main__":
    test_5_random_files()
