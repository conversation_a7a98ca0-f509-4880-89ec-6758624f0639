import json
import re
import logging
from typing import Dict, List, Any, Tuple
import numpy as np

logger = logging.getLogger(__name__)

class IntelligentMedicalExtractor:
    
    def __init__(self):
        self.field_descriptions = self._load_field_descriptions()
        self.medical_keywords = self._load_medical_keywords()
        self.name_indicators = self._load_name_indicators()
        
    def _load_field_descriptions(self) -> Dict[str, Dict]:
        return {
            "patient_phone_number": {
                "keywords": ["patient", "pt", "phone", "tel", "telephone", "cell", "mobile", "contact"],
                "context": "patient information section",
                "format": "phone_number",
                "exclusions": ["doctor", "physician", "provider", "facility"]
            },
            "doctor_phone_number": {
                "keywords": ["doctor", "dr", "physician", "provider", "referring", "ordering", "phone", "tel"],
                "context": "physician information section", 
                "format": "phone_number",
                "exclusions": ["patient", "pt"]
            },
            "doctor_fax": {
                "keywords": ["fax", "facsimile"],
                "context": "physician contact information",
                "format": "phone_number",
                "exclusions": ["patient"]
            },
            "first_name": {
                "keywords": ["patient", "first", "given", "name"],
                "context": "patient demographics",
                "format": "proper_name",
                "exclusions": ["doctor", "physician", "last", "family"]
            },
            "last_name": {
                "keywords": ["patient", "last", "family", "surname", "name"],
                "context": "patient demographics", 
                "format": "proper_name",
                "exclusions": ["doctor", "physician", "first", "given"]
            },
            "fullname": {
                "keywords": ["patient", "name", "full"],
                "context": "patient identification",
                "format": "full_name",
                "exclusions": ["doctor", "physician"]
            },
            "birthDate": {
                "keywords": ["dob", "birth", "born", "date of birth", "birthday"],
                "context": "patient demographics",
                "format": "date",
                "exclusions": ["exam", "study", "procedure"]
            },
            "gender": {
                "keywords": ["gender", "sex", "male", "female", "m", "f"],
                "context": "patient demographics",
                "format": "gender",
                "exclusions": []
            },
            "doctor_X": {
                "keywords": ["doctor", "dr", "physician", "referring", "ordering", "provider"],
                "context": "physician information",
                "format": "doctor_name",
                "exclusions": ["patient"]
            },
            "doctor_NPI": {
                "keywords": ["npi", "provider", "national provider", "identifier"],
                "context": "physician credentials",
                "format": "npi",
                "exclusions": ["patient"]
            },
            "doctor_address": {
                "keywords": ["address", "location", "street", "facility"],
                "context": "physician facility information",
                "format": "address",
                "exclusions": ["patient"]
            },
            "doctor_city": {
                "keywords": ["city", "location"],
                "context": "physician facility location",
                "format": "city",
                "exclusions": ["patient"]
            },
            "doctor_state": {
                "keywords": ["state", "province"],
                "context": "physician facility location",
                "format": "state",
                "exclusions": ["patient"]
            },
            "icd_codes": {
                "keywords": ["icd", "diagnosis", "diagnostic", "code"],
                "context": "medical diagnosis",
                "format": "icd_code",
                "exclusions": ["cpt", "procedure"]
            },
            "modality": {
                "keywords": ["modality", "imaging", "study", "exam", "ct", "mri", "xray", "ultrasound", "mammography"],
                "context": "imaging study information",
                "format": "modality",
                "exclusions": []
            },
            "exam": {
                "keywords": ["exam", "study", "procedure", "imaging", "scan"],
                "context": "imaging procedure description",
                "format": "exam_description",
                "exclusions": []
            },
            "bodyPart": {
                "keywords": ["anatomy", "body", "part", "region", "area", "site"],
                "context": "anatomical region being examined",
                "format": "body_part",
                "exclusions": []
            },
            "insurance_number": {
                "keywords": ["insurance", "policy", "member", "subscriber", "id", "number"],
                "context": "insurance information",
                "format": "insurance_id",
                "exclusions": ["npi", "provider"]
            },
            "doctor_exam_codes": {
                "keywords": ["cpt", "procedure", "code", "billing"],
                "context": "procedure billing codes",
                "format": "procedure_code",
                "exclusions": ["icd", "diagnosis"]
            }
        }
    
    def _load_medical_keywords(self) -> Dict[str, List[str]]:
        return {
            "anatomical_parts": [
                "head", "brain", "skull", "neck", "throat", "chest", "lung", "heart", "breast",
                "abdomen", "stomach", "liver", "kidney", "pelvis", "hip", "spine", "back",
                "shoulder", "arm", "elbow", "wrist", "hand", "finger", "leg", "knee", 
                "ankle", "foot", "toe", "lumbar", "cervical", "thoracic", "sacral"
            ],
            "modalities": [
                "ct", "cat", "computed tomography", "mri", "magnetic resonance", "mr",
                "xray", "x-ray", "radiograph", "ultrasound", "us", "sonography", "echo",
                "mammography", "mammogram", "mm", "pet", "nuclear", "fluoroscopy"
            ],
            "directions": [
                "right", "left", "bilateral", "anterior", "posterior", "superior", 
                "inferior", "medial", "lateral", "proximal", "distal", "r", "l", "rt", "lt"
            ]
        }
    
    def _load_name_indicators(self) -> List[str]:
        return [
            "patient", "pt", "name", "first", "last", "given", "family", "surname",
            "mr", "mrs", "ms", "miss", "dr", "doctor", "physician"
        ]
    
    def extract_fields(self, text: str) -> Dict[str, str]:
        text_lines = text.split('\n')
        text_lower = text.lower()
        
        results = {}
        
        for field_name, field_config in self.field_descriptions.items():
            extracted_value = self._extract_field_intelligent(
                text, text_lines, text_lower, field_name, field_config
            )
            results[field_name] = extracted_value
        
        results = self._post_process_results(results, text)
        
        return results
    
    def _extract_field_intelligent(self, text: str, text_lines: List[str], 
                                 text_lower: str, field_name: str, 
                                 field_config: Dict) -> str:
        
        keywords = field_config.get("keywords", [])
        exclusions = field_config.get("exclusions", [])
        format_type = field_config.get("format", "")
        
        candidate_lines = self._find_candidate_lines(text_lines, keywords, exclusions)
        
        if not candidate_lines:
            return ""
        
        if format_type == "phone_number":
            return self._extract_phone_number(candidate_lines, field_name)
        elif format_type == "date":
            return self._extract_date(candidate_lines)
        elif format_type == "proper_name":
            return self._extract_name(candidate_lines, field_name)
        elif format_type == "full_name":
            return self._extract_full_name(candidate_lines)
        elif format_type == "gender":
            return self._extract_gender(candidate_lines)
        elif format_type == "doctor_name":
            return self._extract_doctor_name(candidate_lines)
        elif format_type == "npi":
            return self._extract_npi(candidate_lines)
        elif format_type == "address":
            return self._extract_address(candidate_lines)
        elif format_type == "city":
            return self._extract_city(candidate_lines)
        elif format_type == "state":
            return self._extract_state(candidate_lines)
        elif format_type == "icd_code":
            return self._extract_icd_codes(candidate_lines)
        elif format_type == "modality":
            return self._extract_modality(candidate_lines, text_lower)
        elif format_type == "exam_description":
            return self._extract_exam(candidate_lines)
        elif format_type == "body_part":
            return self._extract_body_part(candidate_lines, text_lower)
        elif format_type == "insurance_id":
            return self._extract_insurance_number(candidate_lines)
        elif format_type == "procedure_code":
            return self._extract_procedure_codes(candidate_lines)
        
        return ""
    
    def _find_candidate_lines(self, text_lines: List[str], keywords: List[str], 
                            exclusions: List[str]) -> List[str]:
        candidate_lines = []
        
        for line in text_lines:
            line_lower = line.lower().strip()
            if not line_lower:
                continue
            
            has_keyword = any(keyword.lower() in line_lower for keyword in keywords)
            has_exclusion = any(exclusion.lower() in line_lower for exclusion in exclusions)
            
            if has_keyword and not has_exclusion:
                candidate_lines.append(line.strip())
                
                for i, text_line in enumerate(text_lines):
                    if text_line.strip() == line.strip():
                        if i > 0:
                            candidate_lines.append(text_lines[i-1].strip())
                        if i < len(text_lines) - 1:
                            candidate_lines.append(text_lines[i+1].strip())
                        break
        
        return list(set(candidate_lines))
    
    def _extract_phone_number(self, lines: List[str], field_type: str) -> str:
        phone_patterns = [
            r'\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}',
            r'\d{3}[-.\s]\d{3}[-.\s]\d{4}',
            r'\(\d{3}\)\s?\d{3}[-.\s]\d{4}'
        ]
        
        for line in lines:
            for pattern in phone_patterns:
                matches = re.findall(pattern, line)
                if matches:
                    phone = matches[0]
                    digits = re.sub(r'[^\d]', '', phone)
                    if len(digits) == 10:
                        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
                    elif len(digits) == 11 and digits.startswith('1'):
                        digits = digits[1:]
                        return f"{digits[:3]}-{digits[3:6]}-{digits[6:]}"
        
        return ""
    
    def _extract_date(self, lines: List[str]) -> str:
        date_patterns = [
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
            r'(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})',
            r'(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})'
        ]
        
        for line in lines:
            for pattern in date_patterns:
                match = re.search(pattern, line)
                if match:
                    parts = match.groups()
                    if len(parts[0]) == 4:
                        year, month, day = parts
                    elif len(parts[2]) == 4:
                        if int(parts[0]) > 12:
                            day, month, year = parts
                        else:
                            month, day, year = parts
                    else:
                        if int(parts[0]) > 12:
                            day, month, year = parts
                        else:
                            month, day, year = parts
                        year = f"20{year}" if int(year) < 50 else f"19{year}"
                    
                    return f"{day.zfill(2)}/{month.zfill(2)}/{year}"
        
        return ""
    
    def _extract_name(self, lines: List[str], field_type: str) -> str:
        name_patterns = [
            r'\b([A-Z][a-z]+)\b',
            r'([A-Z][A-Z\s]+)',
            r'([a-zA-Z]+)'
        ]
        
        for line in lines:
            line_clean = re.sub(r'[^\w\s]', ' ', line)
            words = line_clean.split()
            
            for word in words:
                if len(word) > 1 and word.isalpha():
                    if word.lower() not in ['patient', 'name', 'first', 'last', 'dr', 'doctor']:
                        return word.capitalize()
        
        return ""
    
    def _extract_full_name(self, lines: List[str]) -> str:
        for line in lines:
            line_clean = re.sub(r'[^\w\s]', ' ', line)
            words = [w for w in line_clean.split() if w.isalpha() and len(w) > 1]
            
            if len(words) >= 2:
                filtered_words = [w for w in words if w.lower() not in 
                                ['patient', 'name', 'first', 'last', 'dr', 'doctor', 'mr', 'mrs', 'ms']]
                
                if len(filtered_words) >= 2:
                    first = filtered_words[0].capitalize()
                    last = filtered_words[-1].capitalize()
                    middle = ' '.join([w.capitalize() for w in filtered_words[1:-1]]) if len(filtered_words) > 2 else ''
                    
                    if middle:
                        return f"{last}, {first} {middle}"
                    else:
                        return f"{last}, {first}"
        
        return ""
    
    def _extract_gender(self, lines: List[str]) -> str:
        for line in lines:
            line_lower = line.lower()
            if 'male' in line_lower and 'female' not in line_lower:
                return 'Male'
            elif 'female' in line_lower:
                return 'Female'
            elif re.search(r'\bm\b', line_lower):
                return 'Male'
            elif re.search(r'\bf\b', line_lower):
                return 'Female'
        
        return ""
    
    def _extract_doctor_name(self, lines: List[str]) -> str:
        for line in lines:
            line_clean = re.sub(r'\b(?:dr\.?|doctor|md|do|phd)\b', '', line, flags=re.IGNORECASE)
            line_clean = re.sub(r'[^\w\s]', ' ', line_clean)
            words = [w for w in line_clean.split() if w.isalpha() and len(w) > 1]
            
            if len(words) >= 2:
                filtered_words = [w for w in words if w.lower() not in 
                                ['referring', 'ordering', 'physician', 'provider']]
                
                if len(filtered_words) >= 2:
                    return ' '.join([w.capitalize() for w in filtered_words[:3]])
        
        return ""
    
    def _extract_npi(self, lines: List[str]) -> str:
        for line in lines:
            npi_match = re.search(r'\b(\d{10})\b', line)
            if npi_match:
                return npi_match.group(1)
        
        return ""
    
    def _extract_address(self, lines: List[str]) -> str:
        address_patterns = [
            r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)',
            r'\d+\s+[A-Za-z\s]+'
        ]
        
        for line in lines:
            for pattern in address_patterns:
                match = re.search(pattern, line, re.IGNORECASE)
                if match:
                    address = match.group(0).strip()
                    address = re.sub(r'\bSte\b', 'Suite', address, flags=re.IGNORECASE)
                    return address
        
        return ""
    
    def _extract_city(self, lines: List[str]) -> str:
        for line in lines:
            city_match = re.search(r',\s*([A-Za-z\s]+),\s*[A-Z]{2}', line)
            if city_match:
                return city_match.group(1).strip().title()
            
            words = [w for w in line.split() if w.isalpha() and len(w) > 2]
            if words:
                return words[0].title()
        
        return ""
    
    def _extract_state(self, lines: List[str]) -> str:
        state_codes = {
            'alabama': 'AL', 'alaska': 'AK', 'arizona': 'AZ', 'arkansas': 'AR', 'california': 'CA',
            'colorado': 'CO', 'connecticut': 'CT', 'delaware': 'DE', 'florida': 'FL', 'georgia': 'GA',
            'hawaii': 'HI', 'idaho': 'ID', 'illinois': 'IL', 'indiana': 'IN', 'iowa': 'IA',
            'kansas': 'KS', 'kentucky': 'KY', 'louisiana': 'LA', 'maine': 'ME', 'maryland': 'MD',
            'massachusetts': 'MA', 'michigan': 'MI', 'minnesota': 'MN', 'mississippi': 'MS', 'missouri': 'MO',
            'montana': 'MT', 'nebraska': 'NE', 'nevada': 'NV', 'new hampshire': 'NH', 'new jersey': 'NJ',
            'new mexico': 'NM', 'new york': 'NY', 'north carolina': 'NC', 'north dakota': 'ND', 'ohio': 'OH',
            'oklahoma': 'OK', 'oregon': 'OR', 'pennsylvania': 'PA', 'rhode island': 'RI', 'south carolina': 'SC',
            'south dakota': 'SD', 'tennessee': 'TN', 'texas': 'TX', 'utah': 'UT', 'vermont': 'VT',
            'virginia': 'VA', 'washington': 'WA', 'west virginia': 'WV', 'wisconsin': 'WI', 'wyoming': 'WY'
        }
        
        for line in lines:
            state_match = re.search(r'\b([A-Z]{2})\b', line)
            if state_match:
                return state_match.group(1)
            
            for state_name, code in state_codes.items():
                if state_name.lower() in line.lower():
                    return code
        
        return ""
    
    def _extract_icd_codes(self, lines: List[str]) -> str:
        codes = []
        for line in lines:
            icd_matches = re.findall(r'\b([A-Z]\d{2}\.?\d{0,2})\b', line)
            codes.extend(icd_matches)
        
        return ', '.join(codes[:3]) if codes else ""
    
    def _extract_modality(self, lines: List[str], full_text_lower: str) -> str:
        modality_map = {
            'ct': 'CT', 'cat': 'CT', 'computed tomography': 'CT',
            'mri': 'MRI', 'magnetic resonance': 'MRI', 'mr': 'MRI',
            'xray': 'XR', 'x-ray': 'XR', 'radiograph': 'XR',
            'ultrasound': 'US', 'sonography': 'US', 'echo': 'US',
            'mammography': 'MM', 'mammogram': 'MM'
        }
        
        for modality, code in modality_map.items():
            if modality in full_text_lower:
                return code
        
        return ""
    
    def _extract_exam(self, lines: List[str]) -> str:
        for line in lines:
            if any(word in line.lower() for word in ['ct', 'mri', 'xray', 'ultrasound', 'scan']):
                return line.strip().title()
        
        return ""
    
    def _extract_body_part(self, lines: List[str], full_text_lower: str) -> str:
        anatomical_parts = self.medical_keywords["anatomical_parts"]
        directions = self.medical_keywords["directions"]
        
        found_parts = []
        found_directions = []
        
        for part in anatomical_parts:
            if part in full_text_lower:
                found_parts.append(part)
        
        for direction in directions:
            if direction in full_text_lower:
                found_directions.append(direction)
        
        if found_parts:
            result = found_parts[0].title()
            if found_directions:
                direction = found_directions[0]
                if direction.lower() in ['r', 'rt']:
                    result = f"Right {result}"
                elif direction.lower() in ['l', 'lt']:
                    result = f"Left {result}"
                elif direction.lower() in ['right', 'left']:
                    result = f"{direction.title()} {result}"
            
            return result
        
        return ""
    
    def _extract_insurance_number(self, lines: List[str]) -> str:
        for line in lines:
            insurance_match = re.search(r'\b([A-Z0-9]{6,})\b', line)
            if insurance_match:
                return insurance_match.group(1)
        
        return ""
    
    def _extract_procedure_codes(self, lines: List[str]) -> str:
        codes = []
        for line in lines:
            code_matches = re.findall(r'\b(\d{5})\b', line)
            codes.extend(code_matches)
        
        return ', '.join(codes) if codes else ""
    
    def _post_process_results(self, results: Dict[str, str], full_text: str) -> Dict[str, str]:
        if not results.get('fullname') and results.get('first_name') and results.get('last_name'):
            first = results['first_name']
            last = results['last_name']
            results['fullname'] = f"{last}, {first}"
        
        if results.get('fullname') and not results.get('first_name'):
            if ',' in results['fullname']:
                parts = results['fullname'].split(',')
                if len(parts) >= 2:
                    results['last_name'] = parts[0].strip()
                    name_parts = parts[1].strip().split()
                    if name_parts:
                        results['first_name'] = name_parts[0]
        
        return results

def model_fn(model_dir):
    return IntelligentMedicalExtractor()

def input_fn(request_body, content_type='application/json'):
    if content_type == 'application/json':
        input_data = json.loads(request_body)
        return input_data
    else:
        raise ValueError(f"Unsupported content type: {content_type}")

def predict_fn(input_data, model):
    text = input_data.get('text', '')
    fields = input_data.get('fields', [])
    
    logger.info(f"Processing {len(text)} characters for {len(fields)} fields")
    
    extracted_fields = model.extract_fields(text)
    
    if fields:
        filtered_results = {field: extracted_fields.get(field, "") for field in fields}
        return filtered_results
    
    return extracted_fields

def output_fn(prediction, accept='application/json'):
    if accept == 'application/json':
        return json.dumps(prediction)
    else:
        raise ValueError(f"Unsupported accept type: {accept}")
