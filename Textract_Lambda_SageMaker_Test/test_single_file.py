import sys
import os
import json
import time

sys.path.append('/Users/<USER>/Documents/OneImaging-Incoming-Fax-Automation')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ground_truth_data import GROUND_TRUTH_DATA
from llm_extractor import LLMBasedExtractor

def test_single_file():
    """Test extraction on 531082057.pdf and show detailed results"""
    
    filename = "531082057.pdf"
    
    print("TESTING SINGLE FILE: 531082057.pdf")
    print("=" * 60)
    
    # Show ground truth for comparison
    if filename in GROUND_TRUTH_DATA:
        print("GROUND TRUTH DATA:")
        print("-" * 30)
        ground_truth = GROUND_TRUTH_DATA[filename]
        for field, value in ground_truth.items():
            print(f"  {field:20}: '{value}'")
    
    print(f"\n{'='*60}")
    print("LLM EXTRACTION PROCESS")
    print("=" * 60)
    
    try:
        extractor = LLMBasedExtractor()
        file_key = f"Imaging Orders OCR Data/{filename}"
        
        print(f"1. Extracting text from s3://data-automation-dev-bucket/{file_key}")
        start_time = time.time()
        
        # Extract fields using LLM
        extracted_fields = extractor.extract_fields_from_pdf("data-automation-dev-bucket", file_key)
        
        processing_time = time.time() - start_time
        
        if extracted_fields:
            print(f"2. ✅ Extraction completed in {processing_time:.1f} seconds")
            
            print(f"\nLLM EXTRACTED FIELDS:")
            print("-" * 30)
            for field, value in extracted_fields.items():
                print(f"  {field:20}: '{value}'")
            
            # Compare with ground truth
            if filename in GROUND_TRUTH_DATA:
                print(f"\n{'='*60}")
                print("FIELD-BY-FIELD COMPARISON")
                print("=" * 60)
                
                ground_truth = GROUND_TRUTH_DATA[filename]
                matches = 0
                total = 0
                
                for field_name in ground_truth.keys():
                    gt_value = str(ground_truth[field_name]).strip()
                    llm_value = str(extracted_fields.get(field_name, "")).strip()
                    
                    # Simple comparison
                    is_match = False
                    if gt_value.lower() == llm_value.lower():
                        is_match = True
                    elif gt_value and llm_value:
                        from difflib import SequenceMatcher
                        similarity = SequenceMatcher(None, gt_value.lower(), llm_value.lower()).ratio()
                        is_match = similarity >= 0.8
                    elif not gt_value and not llm_value:
                        is_match = True
                    
                    status = "✅ MATCH" if is_match else "❌ MISS"
                    print(f"{status:8} {field_name:20}: GT='{gt_value}' | LLM='{llm_value}'")
                    
                    if is_match:
                        matches += 1
                    total += 1
                
                accuracy = (matches / total) * 100 if total > 0 else 0
                print(f"\n📊 ACCURACY: {matches}/{total} = {accuracy:.1f}%")
            
            # Save results to JSON
            result_data = {
                "filename": filename,
                "processing_time_seconds": processing_time,
                "extraction_successful": True,
                "extracted_fields": extracted_fields,
                "ground_truth": GROUND_TRUTH_DATA.get(filename, {}),
                "accuracy_percentage": accuracy if 'accuracy' in locals() else 0,
                "matches": matches if 'matches' in locals() else 0,
                "total_fields": total if 'total' in locals() else 0
            }
            
            output_file = f"{filename}_llm_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Results saved to: {output_file}")
            
        else:
            print("❌ Failed to extract fields")
            
    except Exception as e:
        print(f"❌ Error processing {filename}: {str(e)}")

if __name__ == "__main__":
    test_single_file()
