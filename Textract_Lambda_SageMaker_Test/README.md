# Medical PDF Text Extraction and Field Processing

This project uses AWS Lambda, Textract, and SageMaker to extract text from multi-page medical PDFs and identify specific medical fields using the existing data-automation-dev-bucket.

## Architecture Overview

```
data-automation-dev-bucket → Lambda (Textract) → SageMaker (Field Extraction) → Formatted Results
```

## Components

### 1. Lambda Function (lambda_function/)
- Triggered by S3 uploads to data-automation-dev-bucket
- Uses Textract to extract text from multi-page PDFs
- Calls SageMaker endpoint for field extraction
- Returns formatted medical data

### 2. SageMaker Model (sagemaker_model/)
- Custom model for medical field extraction
- Trained to identify specific medical fields
- Deployed as real-time endpoint

### 3. Infrastructure (infrastructure/)
- CloudFormation templates
- IAM roles and policies
- S3 bucket configuration

## Medical Fields Extracted

- Patient Information: Name, DOB, Phone, Gender
- Doctor Information: Name, Phone, Fax, Address, NPI
- Medical Details: ICD codes, Exam type, Modality, Body part
- Insurance: Insurance number

## Setup Instructions

### Prerequisites
- AWS CLI configured with SSO
- Python 3.9+
- Docker (for SageMaker model)

### 1. Configure AWS SSO
```bash
aws configure sso
# SSO start URL: https://d-9067e3d8b0.awsapps.com/start/#
# SSO Region: us-east-1
```

### 2. Deploy Infrastructure
```bash
cd infrastructure
./deploy.sh
```

### 3. Deploy SageMaker Model
```bash
cd sagemaker_model
python deploy_model.py
```

### 4. Deploy Lambda Function
```bash
cd lambda_function
./deploy_lambda.sh
```

## Usage

1. Medical PDFs are already in data-automation-dev-bucket
2. Lambda function processes PDFs when triggered
3. Results are stored in the output S3 bucket
4. Formatted JSON response contains extracted medical fields

## Field Formatting Rules

- modality: ALL CAPS (CT, MRI, US, XR)
- doctor_state: 2-letter codes (FL, TX, CA)
- bodyPart: Title Case with directions (Right Shoulder, Left Knee)
- phone numbers: XXX-XXX-XXXX format
- fullname: "Last, First Middle" format
- gender: "Male" or "Female"
- birthDate: DD/MM/YYYY format

## Testing

```bash
python test_with_existing_files.py --list-files
python test_with_existing_files.py --test-textract filename.pdf
```

## Monitoring

- CloudWatch logs for Lambda execution
- SageMaker endpoint metrics
- S3 event notifications

## Cost Optimization

- Lambda: Pay per execution
- Textract: Pay per page processed
- SageMaker: Real-time endpoint with auto-scaling
- S3: Standard storage for PDFs and results
