#!/usr/bin/env python3

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

from bedrock_tester import BedrockDataAutomationTester
from ground_truth_helper import GroundTruthManager, create_test_cases_from_s3_bucket

def main():
    load_dotenv()
    
    PROJECT_ARN = os.getenv('BEDROCK_PROJECT_ARN')
    PROFILE_ARN = os.getenv('BEDROCK_PROFILE_ARN')
    INPUT_BUCKET = os.getenv('INPUT_S3_BUCKET')
    OUTPUT_BUCKET = os.getenv('OUTPUT_S3_BUCKET')
    AWS_REGION = os.getenv('AWS_REGION', 'us-east-1')

    if not all([PROJECT_ARN, INPUT_BUCKET, OUTPUT_BUCKET]):
        print("Error: Missing required environment variables")
        print("Please set the following in your .env file:")
        print("- BEDROCK_PROJECT_ARN")
        print("- BEDROCK_PROFILE_ARN (optional, will be derived from project ARN if not provided)")
        print("- INPUT_S3_BUCKET")
        print("- OUTPUT_S3_BUCKET")
        print("\nSee .env.example for the format")
        sys.exit(1)
    
    print(f"Using Bedrock Project: {PROJECT_ARN}")
    print(f"Input S3 Bucket: {INPUT_BUCKET}")
    print(f"Output S3 Bucket: {OUTPUT_BUCKET}")
    print(f"AWS Region: {AWS_REGION}")
    
    from ground_truth_data import GROUND_TRUTH_DATA

    ground_truth_manager = GroundTruthManager()
    ground_truth_manager.ground_truth_data = GROUND_TRUTH_DATA
    
    print(f"Loaded ground truth for {len(ground_truth_manager.list_documents())} documents")
    print(f"Fields to test: {', '.join(ground_truth_manager.get_all_fields())}")

    tester = BedrockDataAutomationTester(
        project_arn=PROJECT_ARN,
        profile_arn=PROFILE_ARN,
        region_name=AWS_REGION,
        input_s3_bucket=INPUT_BUCKET,
        output_s3_bucket=OUTPUT_BUCKET
    )

    test_cases = []
    for document_name, ground_truth in GROUND_TRUTH_DATA.items():
        test_cases.append({
            's3_input_uri': f's3://{INPUT_BUCKET}/Imaging Orders OCR Data/{document_name}',
            'ground_truth': ground_truth,
            'document_name': document_name
        })
    
    if not test_cases:
        print("No test cases found. Make sure:")
        print("1. Your S3 bucket contains PDF files")
        print("2. Your ground truth file has matching document names")
        sys.exit(1)
    
    print(f"\nStarting batch testing of {len(test_cases)} documents...")
    
    results = tester.run_batch_tests(test_cases)
    
    print(f"\nCompleted testing {len(results)} documents")
    
    report_df = tester.generate_report("accuracy_report.csv")
    
    print("\nField-level accuracy and similarity summary:")
    field_columns = [col for col in report_df.columns if col.startswith('field_') and col.endswith('_accuracy')]

    for col in field_columns:
        field_name = col.replace('field_', '').replace('_accuracy', '')
        avg_accuracy = report_df[col].mean()
        similarity_col = f'field_{field_name}_similarity'
        avg_similarity = report_df[similarity_col].mean() if similarity_col in report_df.columns else 0.0
        print(f"  {field_name}: {avg_accuracy:.2%} accuracy, {avg_similarity:.2%} similarity")
    
    print(f"\nDetailed results saved to accuracy_report.csv")

if __name__ == "__main__":
    main()
