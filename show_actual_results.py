#!/usr/bin/env python3
"""
Script to show actual extracted vs expected values for poorly performing documents.
"""

import os
import json
import boto3
from ground_truth_data import GROUND_TRUTH_DATA

def show_actual_vs_expected():
    # Initialize S3 client
    s3_client = boto3.client('s3')
    
    OUTPUT_BUCKET = "data-automation-dev-bucket-output"
    
    # Documents to analyze (poor performers)
    poor_performers = [
        "531087480.pdf",  # 46.56% accuracy
        "537728778.pdf",  # 45.71% accuracy  
        "541560108.pdf",  # 46.03% accuracy
    ]
    
    print("🔍 ACTUAL EXTRACTED vs EXPECTED VALUES")
    print("=" * 80)
    
    for doc_name in poor_performers:
        if doc_name not in GROUND_TRUTH_DATA:
            continue
            
        print(f"\n📄 DOCUMENT: {doc_name}")
        print("-" * 60)
        
        # Try to find the results file
        possible_keys = [
            f"results/{doc_name}_results.json",
            f"output/{doc_name}_results.json",
            f"{doc_name}_results.json",
            f"results/{doc_name.replace('.pdf', '')}_results.json"
        ]
        
        extracted_data = None
        found_key = None
        
        for key in possible_keys:
            try:
                response = s3_client.get_object(Bucket=OUTPUT_BUCKET, Key=key)
                extracted_data = json.loads(response['Body'].read().decode('utf-8'))
                found_key = key
                break
            except:
                continue
        
        if extracted_data:
            print(f"✅ Found results at: {found_key}")
            ground_truth = GROUND_TRUTH_DATA[doc_name]
            
            # Show all fields with mismatches
            print("\n🔍 FIELD-BY-FIELD COMPARISON:")
            
            for field, expected_value in ground_truth.items():
                extracted_value = extracted_data.get(field, "NOT_FOUND")
                
                expected_str = str(expected_value).strip()
                extracted_str = str(extracted_value).strip() if extracted_value is not None else "None"
                
                is_match = expected_str == extracted_str
                status = "✅ MATCH" if is_match else "❌ MISMATCH"
                
                print(f"\n  {field:20} {status}")
                print(f"    Expected:  '{expected_str}'")
                print(f"    Extracted: '{extracted_str}'")
                
                if not is_match:
                    # Show similarity if strings are similar
                    if len(expected_str) > 0 and len(extracted_str) > 0:
                        similarity = calculate_similarity(expected_str, extracted_str)
                        print(f"    Similarity: {similarity:.1f}%")
            
        else:
            print("❌ Could not find results file")
            
            # List available files
            try:
                response = s3_client.list_objects_v2(
                    Bucket=OUTPUT_BUCKET,
                    MaxKeys=20
                )
                
                if 'Contents' in response:
                    print(f"\n📁 Available files in bucket:")
                    for obj in response['Contents'][:10]:
                        print(f"    - {obj['Key']}")
                        
            except Exception as e:
                print(f"  Could not list files: {str(e)}")

def calculate_similarity(str1, str2):
    """Simple similarity calculation"""
    if str1 == str2:
        return 100.0
    
    # Convert to lowercase for comparison
    s1, s2 = str1.lower(), str2.lower()
    
    # Check if one contains the other
    if s1 in s2 or s2 in s1:
        return 80.0
    
    # Check word overlap
    words1 = set(s1.split())
    words2 = set(s2.split())
    
    if words1 and words2:
        overlap = len(words1.intersection(words2))
        total = len(words1.union(words2))
        return (overlap / total) * 100.0
    
    return 0.0

if __name__ == "__main__":
    show_actual_vs_expected()
