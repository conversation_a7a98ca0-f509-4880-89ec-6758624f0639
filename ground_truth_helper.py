import pandas as pd
import json
from typing import Dict, List, Any
from pathlib import Path

class GroundTruthManager:
    def __init__(self):
        self.ground_truth_data = {}
    
    def create_template_csv(self, output_file: str = "ground_truth_template.csv"):
        template_data = {
            'document_name': ['sample_fax_1.pdf', 'sample_fax_2.pdf'],
            'patient_name': ['<PERSON>', '<PERSON>'],
            'date_of_birth': ['1980-01-15', '1975-06-22'],
            'diagnosis': ['Hypertension', 'Diabetes'],
            'medication': ['Lisinopril 10mg', 'Metformin 500mg'],
            'doctor_name': ['Dr. <PERSON>', 'Dr<PERSON> <PERSON>'],
            'visit_date': ['2024-01-15', '2024-01-16']
        }
        
        df = pd.DataFrame(template_data)
        df.to_csv(output_file, index=False)
        print(f"Template CSV created: {output_file}")
        print("Edit this file with your actual ground truth data")
        return df
    
    def load_from_csv(self, csv_file: str) -> Dict[str, Dict[str, Any]]:
        df = pd.read_csv(csv_file)
        ground_truth_data = {}
        
        for _, row in df.iterrows():
            document_name = row['document_name']
            ground_truth = {}
            
            for col in df.columns:
                if col != 'document_name':
                    value = row[col]
                    if pd.notna(value):
                        ground_truth[col] = value
            
            ground_truth_data[document_name] = ground_truth
        
        self.ground_truth_data = ground_truth_data
        return ground_truth_data
    
    def load_from_json(self, json_file: str) -> Dict[str, Dict[str, Any]]:
        with open(json_file, 'r') as f:
            self.ground_truth_data = json.load(f)
        return self.ground_truth_data
    
    def save_to_json(self, output_file: str):
        with open(output_file, 'w') as f:
            json.dump(self.ground_truth_data, f, indent=2)
        print(f"Ground truth data saved to: {output_file}")
    
    def get_ground_truth(self, document_name: str) -> Dict[str, Any]:
        return self.ground_truth_data.get(document_name, {})
    
    def add_ground_truth(self, document_name: str, ground_truth: Dict[str, Any]):
        self.ground_truth_data[document_name] = ground_truth
    
    def list_documents(self) -> List[str]:
        return list(self.ground_truth_data.keys())
    
    def get_all_fields(self) -> set:
        all_fields = set()
        for ground_truth in self.ground_truth_data.values():
            all_fields.update(ground_truth.keys())
        return all_fields

def create_test_cases_from_s3_bucket(bucket_name: str, 
                                   ground_truth_manager: GroundTruthManager,
                                   prefix: str = "") -> List[Dict[str, Any]]:
    import boto3
    
    s3_client = boto3.client('s3')
    
    try:
        response = s3_client.list_objects_v2(Bucket=bucket_name, Prefix=prefix)
        
        if 'Contents' not in response:
            print(f"No objects found in bucket {bucket_name} with prefix {prefix}")
            return []
        
        test_cases = []
        
        for obj in response['Contents']:
            key = obj['Key']
            if key.lower().endswith('.pdf'):
                document_name = key.split('/')[-1]
                ground_truth = ground_truth_manager.get_ground_truth(document_name)
                
                if ground_truth:
                    test_case = {
                        's3_input_uri': f's3://{bucket_name}/{key}',
                        'ground_truth': ground_truth,
                        'document_name': document_name
                    }
                    test_cases.append(test_case)
                else:
                    print(f"Warning: No ground truth found for {document_name}")
        
        print(f"Created {len(test_cases)} test cases from S3 bucket")
        return test_cases
        
    except Exception as e:
        print(f"Error listing S3 objects: {str(e)}")
        return []

if __name__ == "__main__":
    manager = GroundTruthManager()
    
    if not Path("ground_truth_template.csv").exists():
        manager.create_template_csv()
        print("\nPlease edit ground_truth_template.csv with your actual data, then run this script again.")
    else:
        ground_truth_data = manager.load_from_csv("ground_truth_template.csv")
        print(f"Loaded ground truth for {len(ground_truth_data)} documents")
        print(f"Fields: {manager.get_all_fields()}")
        
        manager.save_to_json("ground_truth.json")
