#!/usr/bin/env python3
"""
Script to get the actual extracted results from the nested S3 structure.
"""

import os
import json
import boto3
from ground_truth_data import GROUND_TRUTH_DATA

def get_actual_results():
    s3_client = boto3.client('s3')
    OUTPUT_BUCKET = "data-automation-dev-bucket-output"
    
    # Get the actual result for 531087480.pdf
    doc_name = "531087480.pdf"
    
    print(f"🔍 ACTUAL EXTRACTED vs EXPECTED VALUES")
    print("=" * 80)
    print(f"\n📄 DOCUMENT: {doc_name}")
    print("-" * 60)
    
    # Try to get the actual result file
    result_key = "results/531087480.pdf_results.json/1429c43e-0d2b-414b-8a7b-840609e42010/0/custom_output/0/result.json"
    
    try:
        response = s3_client.get_object(Bucket=OUTPUT_BUCKET, Key=result_key)
        extracted_data = json.loads(response['Body'].read().decode('utf-8'))
        
        print(f"✅ Found results at: {result_key}")
        print(f"📊 Raw extracted data structure:")
        print(json.dumps(extracted_data, indent=2)[:500] + "...")
        
        # Get ground truth
        ground_truth = GROUND_TRUTH_DATA[doc_name]
        
        print(f"\n🔍 FIELD-BY-FIELD COMPARISON:")
        print("=" * 60)
        
        # Extract the actual field values from the inference_result
        if 'inference_result' in extracted_data:
            extracted_fields = extracted_data['inference_result']

            print(f"📋 Extracted fields: {list(extracted_fields.keys())}")

            # Compare each field
            for field, expected_value in ground_truth.items():
                extracted_value = extracted_fields.get(field, "NOT_FOUND")

                expected_str = str(expected_value).strip()
                extracted_str = str(extracted_value).strip() if extracted_value is not None else "None"

                is_match = expected_str == extracted_str
                status = "✅ MATCH" if is_match else "❌ MISMATCH"

                print(f"\n  {field:20} {status}")
                print(f"    Expected:  '{expected_str}'")
                print(f"    Extracted: '{extracted_str}'")

                if not is_match and len(expected_str) > 0 and len(extracted_str) > 0:
                    similarity = calculate_similarity(expected_str, extracted_str)
                    print(f"    Similarity: {similarity:.1f}%")

        else:
            print("❌ Unexpected data structure")
            print("Available keys:", list(extracted_data.keys()))
            
    except Exception as e:
        print(f"❌ Error getting results: {str(e)}")

def calculate_similarity(str1, str2):
    """Simple similarity calculation"""
    if str1 == str2:
        return 100.0
    
    # Convert to lowercase for comparison
    s1, s2 = str1.lower(), str2.lower()
    
    # Check if one contains the other
    if s1 in s2 or s2 in s1:
        return 80.0
    
    # Check word overlap
    words1 = set(s1.split())
    words2 = set(s2.split())
    
    if words1 and words2:
        overlap = len(words1.intersection(words2))
        total = len(words1.union(words2))
        return (overlap / total) * 100.0
    
    return 0.0

if __name__ == "__main__":
    get_actual_results()
